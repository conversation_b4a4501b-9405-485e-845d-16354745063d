import 'package:equatable/equatable.dart';
import '../../models/contact_models.dart';
import 'contact_event.dart';

/// States for contact management
abstract class ContactState extends Equatable {
  const ContactState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class ContactInitial extends ContactState {
  const ContactInitial();
}

/// Loading state
class ContactLoading extends ContactState {
  const ContactLoading();
}

/// Contacts loaded successfully
class ContactsLoaded extends ContactState {
  final List<ContactModel> contacts;
  final List<ContactModel> filteredContacts;
  final String? searchQuery;
  final String? selectedTag;
  final ContactSortOption sortOption;
  final bool isSearching;

  const ContactsLoaded({
    required this.contacts,
    required this.filteredContacts,
    this.searchQuery,
    this.selectedTag,
    this.sortOption = ContactSortOption.nameAsc,
    this.isSearching = false,
  });

  @override
  List<Object?> get props => [
        contacts,
        filteredContacts,
        searchQuery,
        selectedTag,
        sortOption,
        isSearching,
      ];

  ContactsLoaded copyWith({
    List<ContactModel>? contacts,
    List<ContactModel>? filteredContacts,
    String? searchQuery,
    String? selectedTag,
    ContactSortOption? sortOption,
    bool? isSearching,
  }) {
    return ContactsLoaded(
      contacts: contacts ?? this.contacts,
      filteredContacts: filteredContacts ?? this.filteredContacts,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedTag: selectedTag ?? this.selectedTag,
      sortOption: sortOption ?? this.sortOption,
      isSearching: isSearching ?? this.isSearching,
    );
  }
}

/// Contact operation in progress
class ContactOperationInProgress extends ContactState {
  final String operation;

  const ContactOperationInProgress(this.operation);

  @override
  List<Object?> get props => [operation];
}

/// Contact created successfully
class ContactCreated extends ContactState {
  final ContactModel contact;

  const ContactCreated(this.contact);

  @override
  List<Object?> get props => [contact];
}

/// Contact updated successfully
class ContactUpdated extends ContactState {
  final ContactModel contact;

  const ContactUpdated(this.contact);

  @override
  List<Object?> get props => [contact];
}

/// Contact deleted successfully
class ContactDeleted extends ContactState {
  final String contactId;

  const ContactDeleted(this.contactId);

  @override
  List<Object?> get props => [contactId];
}

/// Contacts deleted successfully (bulk)
class ContactsBulkDeleted extends ContactState {
  final List<String> contactIds;
  final int deletedCount;

  const ContactsBulkDeleted({
    required this.contactIds,
    required this.deletedCount,
  });

  @override
  List<Object?> get props => [contactIds, deletedCount];
}

/// VCF export successful
class ContactVcfExported extends ContactState {
  final String filePath;
  final int contactCount;

  const ContactVcfExported({
    required this.filePath,
    required this.contactCount,
  });

  @override
  List<Object?> get props => [filePath, contactCount];
}

/// Device import successful
class ContactsImportedFromDevice extends ContactState {
  final int importedCount;
  final int skippedCount;

  const ContactsImportedFromDevice({
    required this.importedCount,
    required this.skippedCount,
  });

  @override
  List<Object?> get props => [importedCount, skippedCount];
}

/// Sync successful
class ContactsSynced extends ContactState {
  final int syncedCount;
  final DateTime syncTime;

  const ContactsSynced({
    required this.syncedCount,
    required this.syncTime,
  });

  @override
  List<Object?> get props => [syncedCount, syncTime];
}

/// Social media profile opened
class SocialMediaProfileOpened extends ContactState {
  final String platform;
  final String username;

  const SocialMediaProfileOpened({
    required this.platform,
    required this.username,
  });

  @override
  List<Object?> get props => [platform, username];
}

/// Error state
class ContactError extends ContactState {
  final String message;
  final String? errorCode;

  const ContactError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

/// Network error state
class ContactNetworkError extends ContactState {
  final String message;

  const ContactNetworkError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Permission error state
class ContactPermissionError extends ContactState {
  final String message;
  final String permissionType;

  const ContactPermissionError({
    required this.message,
    required this.permissionType,
  });

  @override
  List<Object?> get props => [message, permissionType];
}
