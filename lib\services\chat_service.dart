import 'dart:async';
import 'dart:io';
import 'package:isar/isar.dart';
import '../models/chat_models.dart';
import 'api_service.dart';
import 'cloudinary_service.dart';

/// Service for chat functionality with offline-first approach
class ChatService {
  final Isar _isar;
  final ApiService _apiService;
  final CloudinaryService _cloudinaryService;

  // Stream controllers for real-time updates
  final StreamController<List<ChatMessage>> _messagesController =
      StreamController<List<ChatMessage>>.broadcast();
  final StreamController<List<ChatConversation>> _conversationsController =
      StreamController<List<ChatConversation>>.broadcast();

  ChatService({
    required Isar isar,
    required ApiService apiService,
    required CloudinaryService cloudinaryService,
  })  : _isar = isar,
        _apiService = apiService,
        _cloudinaryService = cloudinaryService;

  /// Stream of messages for current conversation
  Stream<List<ChatMessage>> get messagesStream => _messagesController.stream;

  /// Stream of conversations
  Stream<List<ChatConversation>> get conversationsStream => _conversationsController.stream;

  /// Send a text message
  Future<ChatMessage> sendMessage({
    required String text,
    required String conversationId,
    File? image,
  }) async {
    try {
      // Create local message first
      final localMessage = ChatMessage()
        ..messageId = DateTime.now().millisecondsSinceEpoch.toString()
        ..conversationId = conversationId
        ..content = text
        ..messageType = 'text'
        ..isFromUser = true
        ..timestamp = DateTime.now()
        ..status = 'sending'
        ..needsSync = true;

      // Handle image upload if provided
      if (image != null) {
        localMessage.messageType = 'image';
        localMessage.imageUrl = image.path; // Temporary local path
        
        try {
          // Upload image to Cloudinary
          final uploadResponse = await _cloudinaryService.uploadImage(
            imagePath: image.path,
            folder: 'chat_images',
          );
          localMessage.imageUrl = uploadResponse.secureUrl;
        } catch (e) {
          print('⚠️ Failed to upload image, will retry later: $e');
          // Continue with local path, will sync later
        }
      }

      // Save to local database
      await _isar.writeTxn(() async {
        await _isar.chatMessages.put(localMessage);
      });

      // Emit updated messages
      await _emitMessages(conversationId);

      // Send to backend
      try {
        final response = await _apiService.sendMessage(
          message: text,
          conversationId: conversationId,
        );

        // Update local message with backend response
        localMessage.messageId = response['id']?.toString() ?? localMessage.messageId;
        localMessage.status = 'sent';
        localMessage.needsSync = false;

        await _isar.writeTxn(() async {
          await _isar.chatMessages.put(localMessage);
        });

        // Handle AI response if provided
        if (response['ai_response'] != null) {
          await _handleAiResponse(response['ai_response'], conversationId);
        }

      } catch (e) {
        print('⚠️ Failed to send message to backend, will retry later: $e');
        localMessage.status = 'failed';
        await _isar.writeTxn(() async {
          await _isar.chatMessages.put(localMessage);
        });
      }

      // Emit updated messages
      await _emitMessages(conversationId);

      return localMessage;
    } catch (e) {
      print('❌ Failed to send message: $e');
      throw Exception('Failed to send message: $e');
    }
  }

  /// Handle AI response from backend
  Future<void> _handleAiResponse(Map<String, dynamic> aiResponse, String conversationId) async {
    try {
      final aiMessage = ChatMessage()
        ..messageId = aiResponse['id']?.toString() ?? DateTime.now().millisecondsSinceEpoch.toString()
        ..conversationId = conversationId
        ..content = aiResponse['content']?.toString() ?? ''
        ..messageType = aiResponse['type']?.toString() ?? 'text'
        ..isFromUser = false
        ..timestamp = DateTime.parse(aiResponse['timestamp'] ?? DateTime.now().toIso8601String())
        ..status = 'received'
        ..needsSync = false;

      // Handle image response if provided
      if (aiResponse['image_url'] != null) {
        aiMessage.imageUrl = aiResponse['image_url'].toString();
      }

      // Save AI message
      await _isar.writeTxn(() async {
        await _isar.chatMessages.put(aiMessage);
      });

      // Emit updated messages
      await _emitMessages(conversationId);
    } catch (e) {
      print('❌ Failed to handle AI response: $e');
    }
  }

  /// Get messages for a conversation
  Future<List<ChatMessage>> getMessages(String conversationId) async {
    try {
      final messages = await _isar.chatMessages
          .filter()
          .conversationIdEqualTo(conversationId)
          .sortByTimestamp()
          .findAll();

      return messages;
    } catch (e) {
      print('❌ Failed to get messages: $e');
      return [];
    }
  }

  /// Load messages for a conversation and emit to stream
  Future<void> loadMessages(String conversationId) async {
    await _emitMessages(conversationId);
  }

  /// Emit messages to stream
  Future<void> _emitMessages(String conversationId) async {
    final messages = await getMessages(conversationId);
    _messagesController.add(messages);
  }

  /// Get all conversations
  Future<List<ChatConversation>> getConversations() async {
    try {
      final conversations = await _isar.chatConversations
          .where()
          .sortByLastMessageAtDesc()
          .findAll();

      return conversations;
    } catch (e) {
      print('❌ Failed to get conversations: $e');
      return [];
    }
  }

  /// Load conversations and emit to stream
  Future<void> loadConversations() async {
    final conversations = await getConversations();
    _conversationsController.add(conversations);
  }

  /// Create new conversation
  Future<ChatConversation> createConversation({
    required String title,
    String? description,
  }) async {
    try {
      final conversation = ChatConversation()
        ..conversationId = DateTime.now().millisecondsSinceEpoch.toString()
        ..title = title
        ..description = description
        ..createdAt = DateTime.now()
        ..lastMessageAt = DateTime.now()
        ..messageCount = 0
        ..needsSync = true;

      // Save to local database
      await _isar.writeTxn(() async {
        await _isar.chatConversations.put(conversation);
      });

      // Sync with backend
      try {
        final response = await _apiService.createConversation(
          title: title,
          description: description,
        );

        conversation.conversationId = response['id']?.toString() ?? conversation.conversationId;
        conversation.needsSync = false;

        await _isar.writeTxn(() async {
          await _isar.chatConversations.put(conversation);
        });
      } catch (e) {
        print('⚠️ Failed to sync conversation to backend: $e');
      }

      // Emit updated conversations
      await loadConversations();

      return conversation;
    } catch (e) {
      print('❌ Failed to create conversation: $e');
      throw Exception('Failed to create conversation: $e');
    }
  }

  /// Delete conversation
  Future<bool> deleteConversation(String conversationId) async {
    try {
      // Delete messages first
      final messages = await _isar.chatMessages
          .filter()
          .conversationIdEqualTo(conversationId)
          .findAll();

      await _isar.writeTxn(() async {
        for (final message in messages) {
          await _isar.chatMessages.delete(message.id);
        }
      });

      // Delete conversation
      final conversation = await _isar.chatConversations
          .filter()
          .conversationIdEqualTo(conversationId)
          .findFirst();

      if (conversation != null) {
        await _isar.writeTxn(() async {
          await _isar.chatConversations.delete(conversation.id);
        });
      }

      // Sync deletion with backend
      try {
        await _apiService.deleteConversation(conversationId);
      } catch (e) {
        print('⚠️ Failed to delete conversation from backend: $e');
      }

      // Emit updated conversations
      await loadConversations();

      return true;
    } catch (e) {
      print('❌ Failed to delete conversation: $e');
      return false;
    }
  }

  /// Retry failed messages
  Future<void> retryFailedMessages() async {
    try {
      final failedMessages = await _isar.chatMessages
          .filter()
          .statusEqualTo('failed')
          .findAll();

      for (final message in failedMessages) {
        try {
          final response = await _apiService.sendMessage(
            message: message.content,
            conversationId: message.conversationId,
          );

          message.messageId = response['id']?.toString() ?? message.messageId;
          message.status = 'sent';
          message.needsSync = false;

          await _isar.writeTxn(() async {
            await _isar.chatMessages.put(message);
          });

          // Handle AI response if provided
          if (response['ai_response'] != null) {
            await _handleAiResponse(response['ai_response'], message.conversationId);
          }

        } catch (e) {
          print('⚠️ Failed to retry message ${message.messageId}: $e');
        }
      }
    } catch (e) {
      print('❌ Failed to retry failed messages: $e');
    }
  }

  /// Sync conversations with backend
  Future<void> syncConversations() async {
    try {
      final backendConversations = await _apiService.getConversations();
      
      for (final conversationData in backendConversations) {
        final conversation = ChatConversation.fromApi(conversationData);
        
        await _isar.writeTxn(() async {
          await _isar.chatConversations.put(conversation);
        });
      }

      await loadConversations();
    } catch (e) {
      print('❌ Failed to sync conversations: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _messagesController.close();
    _conversationsController.close();
  }
}
