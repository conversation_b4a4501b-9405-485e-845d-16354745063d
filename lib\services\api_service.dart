import 'dart:io';
import 'package:dio/dio.dart';

/// Service for handling all HTTP API communication with the Darvis backend
class ApiService {
  final Dio _dio;

  ApiService(this._dio);

  // Authentication endpoints
  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _dio.post('/auth/login', data: {
        'email': email,
        'password': password,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<Map<String, dynamic>> register({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      final response = await _dio.post('/auth/register', data: {
        'email': email,
        'password': password,
        'name': name,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    try {
      final response = await _dio.post('/auth/refresh', data: {
        'refresh_token': refreshToken,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<Map<String, dynamic>> exchangeFirebaseToken(String idToken) async {
    try {
      final response = await _dio.post('/auth/firebase-exchange', data: {
        'id_token': idToken,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Sync user profile data to backend
  Future<Map<String, dynamic>> syncUserProfile(Map<String, dynamic> profileData) async {
    try {
      final response = await _dio.post('/auth/sync-profile', data: profileData);
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Update user session tracking for dynamic greetings
  /// Update user session for greeting logic
  Future<Map<String, dynamic>> updateUserSession() async {
    try {
      final response = await _dio.post('/api/v1/users/session', data: {
        'timestamp': DateTime.now().toIso8601String(),
        'session_type': 'app_open',
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Get user session data for greeting logic
  Future<Map<String, dynamic>> getUserSessionData() async {
    try {
      final response = await _dio.get('/api/v1/users/session-data');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  // --- Notification API Methods ---

  /// Create a new notification
  Future<Map<String, dynamic>> createNotification(dynamic notificationDto) async {
    try {
      final response = await _dio.post('/notifications', data: notificationDto.toJson());
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Update notification settings
  Future<Map<String, dynamic>> updateNotificationSettings(dynamic settingsDto) async {
    try {
      final response = await _dio.put('/notifications/settings', data: settingsDto.toJson());
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Mark notification as interacted
  Future<Map<String, dynamic>> markNotificationAsInteracted(String notificationId) async {
    try {
      final response = await _dio.post('/notifications/$notificationId/interact');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Get user notifications
  Future<Map<String, dynamic>> getUserNotifications({int page = 1, int limit = 20}) async {
    try {
      final response = await _dio.get('/notifications', queryParameters: {
        'page': page,
        'limit': limit,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  // --- Profile API Methods ---

  /// Update user profile
  Future<Map<String, dynamic>> updateUserProfile(dynamic profileDto) async {
    try {
      final response = await _dio.put('/api/v1/users/profile', data: profileDto.toJson());
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Update profile picture
  Future<Map<String, dynamic>> updateProfilePicture(dynamic updateRequest) async {
    try {
      final response = await _dio.post('/api/v1/users/profile-picture', data: updateRequest.toJson());
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Get user profile
  Future<Map<String, dynamic>> getUserProfile() async {
    try {
      final response = await _dio.get('/api/v1/users/profile');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Delete user profile picture
  Future<Map<String, dynamic>> deleteProfilePicture() async {
    try {
      final response = await _dio.delete('/api/v1/users/profile-picture');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  // Dashboard API methods
  Future<Map<String, dynamic>> getDashboardData() async {
    try {
      final response = await _dio.get('/api/v1/dashboard/daily-summary');
      return response.data;
    } on DioException catch (e) {
      // Fallback to mock data if backend is not available
      print('⚠️ Backend not available, using mock data: $e');
      await Future.delayed(const Duration(seconds: 1)); // Simulate network delay

      return {
        'user_name': 'David',
        'greeting': 'Good morning! Ready to make today productive?',
        'task_summary': {
          'total_tasks': 12,
          'completed_tasks': 8,
          'pending_tasks': 4,
          'today_tasks': [
            {
              'id': '1',
              'title': 'Discuss project scope with team',
              'description': 'Review requirements and timeline',
              'is_completed': false,
              'priority': 'high',
              'due_date': DateTime.now().toIso8601String(),
            },
            {
              'id': '2',
              'title': 'Complete user research analysis',
              'description': 'Analyze survey results and user feedback',
              'is_completed': false,
              'priority': 'medium',
              'due_date': DateTime.now().add(const Duration(hours: 2)).toIso8601String(),
            },
            {
              'id': '3',
              'title': 'Call with client regarding app changes',
              'description': 'Discuss new feature requests',
              'is_completed': true,
              'priority': 'high',
              'due_date': DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
            },
          ],
        },
        'note_summary': {
          'total_notes': 25,
          'recent_notes': 3,
          'pinned_notes': [
            {
              'id': '1',
              'title': 'Project Ideas',
              'content': 'Collection of innovative project concepts...',
              'is_pinned': true,
              'created_at': DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
              'updated_at': DateTime.now().subtract(const Duration(hours: 3)).toIso8601String(),
            },
          ],
        },
        'upcoming_events': [
          {
            'id': '1',
            'title': 'Team Standup',
            'description': 'Daily team sync meeting',
            'start_time': DateTime.now().add(const Duration(hours: 1)).toIso8601String(),
            'end_time': DateTime.now().add(const Duration(hours: 1, minutes: 30)).toIso8601String(),
            'type': 'meeting',
          },
          {
            'id': '2',
            'title': 'Client Presentation',
            'description': 'Present project progress to client',
            'start_time': DateTime.now().add(const Duration(hours: 4)).toIso8601String(),
            'end_time': DateTime.now().add(const Duration(hours: 5)).toIso8601String(),
            'type': 'presentation',
          },
        ],
        'therapy_progress': {
          'session_streak': 7,
          'current_mood': 'positive',
          'weekly_goal_progress': 85,
          'last_session_date': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
        },
        'quick_actions': [
          {
            'id': '1',
            'title': 'Add Task',
            'icon_path': 'assets/icons/add_task.svg',
            'action': 'add_task',
          },
          {
            'id': '2',
            'title': 'New Note',
            'icon_path': 'assets/icons/note.svg',
            'action': 'new_note',
          },
          {
            'id': '3',
            'title': 'Voice Session',
            'icon_path': 'assets/icons/voice.svg',
            'action': 'voice_session',
          },
        ],
      };
    }
  }

  // Chat endpoints
  Future<Map<String, dynamic>> sendMessage({
    required String message,
    required String conversationId,
  }) async {
    try {
      final response = await _dio.post('/chat/message', data: {
        'message': message,
        'conversation_id': conversationId,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<List<Map<String, dynamic>>> getConversations() async {
    try {
      final response = await _dio.get('/chat/conversations');
      return List<Map<String, dynamic>>.from(response.data);
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<Map<String, dynamic>> createConversation({
    required String title,
    String? description,
  }) async {
    try {
      final response = await _dio.post('/chat/conversations', data: {
        'title': title,
        'description': description,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<void> deleteConversation(String conversationId) async {
    try {
      await _dio.delete('/chat/conversations/$conversationId');
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  // Tasks endpoints
  Future<Map<String, dynamic>> createTask({
    required String title,
    required String description,
  }) async {
    try {
      final response = await _dio.post('/tasks', data: {
        'title': title,
        'description': description,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<List<Map<String, dynamic>>> getTasks() async {
    try {
      final response = await _dio.get('/tasks');
      return List<Map<String, dynamic>>.from(response.data);
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<Map<String, dynamic>> updateTask({
    required String taskId,
    Map<String, dynamic>? updates,
  }) async {
    try {
      final response = await _dio.patch('/tasks/$taskId', data: updates);
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  // Notes endpoints
  Future<Map<String, dynamic>> createNote({
    required String title,
    required String content,
  }) async {
    try {
      final response = await _dio.post('/notes', data: {
        'title': title,
        'content': content,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<List<Map<String, dynamic>>> getNotes() async {
    try {
      final response = await _dio.get('/notes');
      return List<Map<String, dynamic>>.from(response.data);
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<Map<String, dynamic>> updateNote({
    required String noteId,
    Map<String, dynamic>? updates,
  }) async {
    try {
      final response = await _dio.patch('/notes/$noteId', data: updates);
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<Map<String, dynamic>> deleteNote(String noteId) async {
    try {
      final response = await _dio.delete('/notes/$noteId');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<Map<String, dynamic>> deleteTask(String taskId) async {
    try {
      final response = await _dio.delete('/tasks/$taskId');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  // Calendar endpoints
  Future<Map<String, dynamic>> createCalendarEvent({
    required String title,
    required String description,
    required DateTime startTime,
    required DateTime endTime,
    String? type,
    bool? isAllDay,
    String? location,
    List<String>? attendees,
  }) async {
    try {
      final response = await _dio.post('/calendar/events', data: {
        'title': title,
        'description': description,
        'start_time': startTime.toIso8601String(),
        'end_time': endTime.toIso8601String(),
        'type': type,
        'is_all_day': isAllDay,
        'location': location,
        'attendees': attendees,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<List<Map<String, dynamic>>> getCalendarEvents({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String();
      }

      final response = await _dio.get('/calendar/events', queryParameters: queryParams);
      return List<Map<String, dynamic>>.from(response.data);
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<Map<String, dynamic>> updateCalendarEvent({
    required String eventId,
    Map<String, dynamic>? updates,
  }) async {
    try {
      final response = await _dio.patch('/calendar/events/$eventId', data: updates);
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<Map<String, dynamic>> deleteCalendarEvent(String eventId) async {
    try {
      final response = await _dio.delete('/calendar/events/$eventId');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  // Search endpoints
  Future<Map<String, dynamic>> searchNotes({
    required String query,
    String? tag,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _dio.get('/notes/search', queryParameters: {
        'query': query,
        'tag': tag,
        'page': page,
        'limit': limit,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<Map<String, dynamic>> searchTasks({
    required String query,
    String? status,
    String? priority,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _dio.get('/tasks/search', queryParameters: {
        'query': query,
        'status': status,
        'priority': priority,
        'page': page,
        'limit': limit,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  Future<Map<String, dynamic>> searchCalendarEvents({
    required String query,
    String? type,
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _dio.get('/calendar/events/search', queryParameters: {
        'query': query,
        'type': type,
        'start_date': startDate?.toIso8601String(),
        'end_date': endDate?.toIso8601String(),
        'page': page,
        'limit': limit,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  // --- Contact Management API Methods ---

  /// Create a new contact
  Future<Map<String, dynamic>> createContact({
    required String name,
    required String phone,
    String? email,
    String? location,
    String? metAt,
    Map<String, String>? socialMedia,
    String? memoryPrompt,
    String? imagePath,
    String? imagePublicId,
    String deviceSyncStatus = 'disabled',
  }) async {
    try {
      final response = await _dio.post('/api/v1/contacts/', data: {
        'name': name,
        'phone': phone,
        'email': email,
        'location': location,
        'met_at': metAt,
        'social_media': socialMedia,
        'memory_prompt': memoryPrompt,
        'image_path': imagePath,
        'image_public_id': imagePublicId,
        'device_sync_status': deviceSyncStatus,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Get all contacts with optional search and pagination
  Future<Map<String, dynamic>> getContacts({
    String? search,
    String? platform,
    int limit = 50,
    int offset = 0,
    String sortBy = 'name',
    String sortOrder = 'asc',
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'limit': limit,
        'offset': offset,
        'sort_by': sortBy,
        'sort_order': sortOrder,
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (platform != null && platform.isNotEmpty) {
        queryParams['platform'] = platform;
      }

      final response = await _dio.get('/api/v1/contacts/', queryParameters: queryParams);
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Get a specific contact by ID
  Future<Map<String, dynamic>> getContact(String contactId) async {
    try {
      final response = await _dio.get('/api/v1/contacts/$contactId');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Update an existing contact
  Future<Map<String, dynamic>> updateContact({
    required String contactId,
    String? name,
    String? phone,
    String? email,
    String? location,
    String? metAt,
    Map<String, String>? socialMedia,
    String? memoryPrompt,
    String? imagePath,
    String? imagePublicId,
    String? deviceSyncStatus,
  }) async {
    try {
      final data = <String, dynamic>{};

      if (name != null) data['name'] = name;
      if (phone != null) data['phone'] = phone;
      if (email != null) data['email'] = email;
      if (location != null) data['location'] = location;
      if (metAt != null) data['met_at'] = metAt;
      if (socialMedia != null) data['social_media'] = socialMedia;
      if (memoryPrompt != null) data['memory_prompt'] = memoryPrompt;
      if (imagePath != null) data['image_path'] = imagePath;
      if (imagePublicId != null) data['image_public_id'] = imagePublicId;
      if (deviceSyncStatus != null) data['device_sync_status'] = deviceSyncStatus;

      final response = await _dio.put('/api/v1/contacts/$contactId', data: data);
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Delete a contact (soft delete)
  Future<Map<String, dynamic>> deleteContact(String contactId) async {
    try {
      final response = await _dio.delete('/api/v1/contacts/$contactId');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Generate VCF file for a contact
  Future<String> getContactVCF(String contactId) async {
    try {
      final response = await _dio.get('/api/v1/contacts/$contactId/vcf');
      return response.data as String;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Bulk export contacts
  Future<Map<String, dynamic>> bulkExportContacts({
    required List<String> contactIds,
    String format = 'vcf',
    bool includeImages = false,
  }) async {
    try {
      final response = await _dio.post('/api/v1/contacts/bulk-export', data: {
        'contact_ids': contactIds,
        'format': format,
        'include_images': includeImages,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Export all user contacts
  Future<Map<String, dynamic>> exportAllContacts({
    String format = 'vcf',
    bool includeImages = false,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'format': format,
        'include_images': includeImages,
      };

      final response = await _dio.get('/api/v1/contacts/export-all', queryParameters: queryParams);
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Update device sync status for a contact
  Future<Map<String, dynamic>> updateDeviceSync({
    required String contactId,
    String? deviceContactId,
    required String syncStatus,
    String? syncError,
    Map<String, String>? deviceInfo,
  }) async {
    try {
      final response = await _dio.post('/api/v1/contacts/$contactId/device-sync', data: {
        'device_contact_id': deviceContactId,
        'sync_status': syncStatus,
        'sync_error': syncError,
        'device_info': deviceInfo,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Get sync status for all contacts
  Future<Map<String, dynamic>> getContactsSyncStatus() async {
    try {
      final response = await _dio.get('/api/v1/contacts/sync-status');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Bulk device sync operations
  Future<Map<String, dynamic>> bulkDeviceSync({
    required List<String> contactIds,
    required Map<String, String> deviceInfo,
  }) async {
    try {
      final response = await _dio.post('/api/v1/contacts/bulk-device-sync', data: {
        'contact_ids': contactIds,
        'device_info': deviceInfo,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Advanced contact search
  Future<Map<String, dynamic>> searchContacts({
    required String query,
    String? fields,
    String? platform,
    String? dateFrom,
    String? dateTo,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'q': query,
        'limit': limit,
        'offset': offset,
      };

      if (fields != null) queryParams['fields'] = fields;
      if (platform != null) queryParams['platform'] = platform;
      if (dateFrom != null) queryParams['date_from'] = dateFrom;
      if (dateTo != null) queryParams['date_to'] = dateTo;

      final response = await _dio.get('/api/v1/contacts/search', queryParameters: queryParams);
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  // --- Smart Capture (Frictionless Inbox) API Methods ---

  /// Capture content from URL or text
  Future<Map<String, dynamic>> captureContent({
    required String url,
    String? title,
    String? description,
    List<String>? tags,
  }) async {
    try {
      final response = await _dio.post('/api/v1/inbox/capture', data: {
        'url': url,
        'title': title,
        'description': description,
        'tags': tags,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Upload and process image content
  Future<Map<String, dynamic>> uploadAndProcessImage(File image) async {
    try {
      final fileName = image.path.split('/').last;
      final formData = FormData.fromMap({
        'image': await MultipartFile.fromFile(
          image.path,
          filename: fileName,
        ),
      });

      final response = await _dio.post('/api/v1/inbox/upload-image', data: formData);
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Get all captured content items
  Future<Map<String, dynamic>> getCapturedContent({
    String? search,
    String? contentType,
    String? status,
    List<String>? tags,
    int limit = 20,
    int offset = 0,
    String sortBy = 'created_at',
    String sortOrder = 'desc',
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'limit': limit,
        'offset': offset,
        'sort_by': sortBy,
        'sort_order': sortOrder,
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (contentType != null) queryParams['content_type'] = contentType;
      if (status != null) queryParams['status'] = status;
      if (tags != null && tags.isNotEmpty) {
        queryParams['tags'] = tags.join(',');
      }

      final response = await _dio.get('/api/v1/inbox/items', queryParameters: queryParams);
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Get specific captured content item
  Future<Map<String, dynamic>> getCapturedContentById(String id) async {
    try {
      final response = await _dio.get('/api/v1/inbox/items/$id');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Update captured content item
  Future<Map<String, dynamic>> updateCapturedContent({
    required String id,
    String? title,
    String? summary,
    List<String>? tags,
    String? status,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (title != null) data['title'] = title;
      if (summary != null) data['summary'] = summary;
      if (tags != null) data['tags'] = tags;
      if (status != null) data['status'] = status;

      final response = await _dio.put('/api/v1/inbox/items/$id', data: data);
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Delete captured content item
  Future<void> deleteCapturedContent(String id) async {
    try {
      await _dio.delete('/api/v1/inbox/items/$id');
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Bulk delete captured content items
  Future<Map<String, dynamic>> bulkDeleteCapturedContent(List<String> ids) async {
    try {
      final response = await _dio.post('/api/v1/inbox/bulk-delete', data: {
        'ids': ids,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Search captured content
  Future<Map<String, dynamic>> searchCapturedContent({
    required String query,
    String? contentType,
    List<String>? tags,
    String? dateFrom,
    String? dateTo,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'q': query,
        'limit': limit,
        'offset': offset,
      };

      if (contentType != null) queryParams['content_type'] = contentType;
      if (tags != null && tags.isNotEmpty) {
        queryParams['tags'] = tags.join(',');
      }
      if (dateFrom != null) queryParams['date_from'] = dateFrom;
      if (dateTo != null) queryParams['date_to'] = dateTo;

      final response = await _dio.get('/api/v1/inbox/search', queryParameters: queryParams);
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Get processing status for content
  Future<Map<String, dynamic>> getProcessingStatus(String id) async {
    try {
      final response = await _dio.get('/api/v1/inbox/processing/$id/status');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Retry failed content processing
  Future<Map<String, dynamic>> retryContentProcessing(String id) async {
    try {
      final response = await _dio.post('/api/v1/inbox/retry/$id');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Get available tags for captured content
  Future<Map<String, dynamic>> getCapturedContentTags() async {
    try {
      final response = await _dio.get('/api/v1/inbox/tags');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Get analytics for captured content
  Future<Map<String, dynamic>> getCapturedContentAnalytics({
    String? dateFrom,
    String? dateTo,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (dateFrom != null) queryParams['date_from'] = dateFrom;
      if (dateTo != null) queryParams['date_to'] = dateTo;

      final response = await _dio.get('/api/v1/inbox/analytics', queryParameters: queryParams);
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  // --- Smart Capture Integration API Methods ---

  /// Import captured content to notes
  Future<Map<String, dynamic>> importContentToNotes({
    required String contentId,
    String? noteTitle,
    String? noteContent,
    List<String>? tags,
  }) async {
    try {
      final response = await _dio.post('/api/v1/notes/import', data: {
        'content_id': contentId,
        'title': noteTitle,
        'content': noteContent,
        'tags': tags,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Add captured content to chat context
  Future<Map<String, dynamic>> addContentToChatContext({
    required String contentId,
    String? conversationId,
    String? contextMessage,
  }) async {
    try {
      final response = await _dio.post('/api/v1/chat/context', data: {
        'content_id': contentId,
        'conversation_id': conversationId,
        'context_message': contextMessage,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Create calendar event from captured content
  Future<Map<String, dynamic>> createEventFromContent({
    required String contentId,
    required String title,
    required DateTime startTime,
    required DateTime endTime,
    String? description,
    String? location,
    List<String>? attendees,
  }) async {
    try {
      final response = await _dio.post('/api/v1/calendar/events', data: {
        'content_id': contentId,
        'title': title,
        'start_time': startTime.toIso8601String(),
        'end_time': endTime.toIso8601String(),
        'description': description,
        'location': location,
        'attendees': attendees,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Get contact analytics
  Future<Map<String, dynamic>> getContactAnalytics() async {
    try {
      final response = await _dio.get('/api/v1/contacts/analytics');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Get all social platforms
  Future<Map<String, dynamic>> getSocialPlatforms() async {
    try {
      final response = await _dio.get('/api/v1/social-platforms/');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Get specific social platform configuration
  Future<Map<String, dynamic>> getSocialPlatform(String platform) async {
    try {
      final response = await _dio.get('/api/v1/social-platforms/$platform');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Validate social media username
  Future<Map<String, dynamic>> validateSocialUsername({
    required String platform,
    required String username,
  }) async {
    try {
      final response = await _dio.post('/api/v1/social-platforms/$platform/validate', data: {
        'username': username,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// Verify social media profile (optional feature)
  Future<Map<String, dynamic>> verifySocialProfile({
    required String contactId,
    required String platform,
    required String username,
  }) async {
    try {
      final response = await _dio.post('/api/v1/contacts/$contactId/social-verify', data: {
        'platform': platform,
        'username': username,
      });
      return response.data;
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  // Error handling
  Exception _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return Exception('Connection timeout. Please check your internet connection.');
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['message'] ?? 'Unknown error occurred';
        return Exception('Server error ($statusCode): $message');
      case DioExceptionType.cancel:
        return Exception('Request was cancelled');
      case DioExceptionType.connectionError:
        return Exception('No internet connection');
      default:
        return Exception('Unexpected error: ${e.message}');
    }
  }
}
