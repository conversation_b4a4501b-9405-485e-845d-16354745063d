import 'dart:io';
import 'package:darvis_app/services/api_service.dart';

/// Mock implementation of ApiService for testing
class MockApiService implements ApiService {

  @override
  Future<Map<String, dynamic>> exchangeFirebaseToken(String idToken) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return {
      'access_token': 'mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
      'refresh_token': 'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
    };
  }

  @override
  Future<Map<String, dynamic>> syncUserProfile(Map<String, dynamic> profileData) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return {
      'success': true,
      'user_id': 'mock_user_${DateTime.now().millisecondsSinceEpoch}',
    };
  }

  @override
  Future<Map<String, dynamic>> getDashboardData() async {
    await Future.delayed(const Duration(milliseconds: 800));
    return {
      'tasks': _tasks,
      'notes': _notes,
      'conversations': _conversations,
    };
  }

  @override
  Future<Map<String, dynamic>> updateUserSession() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return {
      'success': true,
      'session_id': 'mock_session_${DateTime.now().millisecondsSinceEpoch}',
    };
  }

  @override
  Future<Map<String, dynamic>> getUserSessionData() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return {
      'daily_sessions': 2,
      'last_session': DateTime.now().subtract(const Duration(hours: 3)).toIso8601String(),
      'first_session_today': DateTime.now().subtract(const Duration(hours: 8)).toIso8601String(),
    };
  }

  @override
  Future<Map<String, dynamic>> createNotification(dynamic notificationDto) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return {
      'success': true,
      'notification_id': 'mock_notification_${DateTime.now().millisecondsSinceEpoch}',
    };
  }

  @override
  Future<Map<String, dynamic>> updateNotificationSettings(dynamic settingsDto) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return {'success': true};
  }

  @override
  Future<Map<String, dynamic>> markNotificationAsInteracted(String notificationId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return {'success': true};
  }

  @override
  Future<Map<String, dynamic>> getUserNotifications({int page = 1, int limit = 20}) async {
    await Future.delayed(const Duration(milliseconds: 400));
    return {
      'notifications': [],
      'total': 0,
      'page': page,
      'limit': limit,
    };
  }

  @override
  Future<Map<String, dynamic>> updateUserProfile(dynamic profileDto) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return {
      'success': true,
      'profile_id': 'mock_profile_${DateTime.now().millisecondsSinceEpoch}',
    };
  }

  @override
  Future<Map<String, dynamic>> updateProfilePicture(dynamic updateRequest) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return {
      'success': true,
      'profile_picture_url': 'https://mock.cloudinary.com/image.jpg',
    };
  }

  @override
  Future<Map<String, dynamic>> getUserProfile() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return {
      'user_id': 'mock_user_123',
      'display_name': 'Mock User',
      'email': '<EMAIL>',
      'profile_picture_url': null,
    };
  }

  @override
  Future<Map<String, dynamic>> deleteProfilePicture() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return {'success': true};
  }
  final List<Map<String, dynamic>> _tasks = [];
  final List<Map<String, dynamic>> _notes = [];
  final List<Map<String, dynamic>> _conversations = [];
  
  int _nextTaskId = 1;
  int _nextNoteId = 1;
  int _nextConversationId = 1;

  /// Simulate network delay
  Future<void> _simulateDelay() async {
    await Future.delayed(const Duration(milliseconds: 300));
  }

  @override
  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    await _simulateDelay();
    
    if (email == '<EMAIL>' && password == 'password123') {
      return {
        'access_token': 'mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
        'refresh_token': 'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
        'user': {
          'id': 'mock_user_id',
          'email': email,
          'name': 'Test User',
        },
      };
    } else {
      throw Exception('Invalid credentials');
    }
  }

  @override
  Future<Map<String, dynamic>> register({
    required String email,
    required String password,
    required String name,
  }) async {
    await _simulateDelay();
    
    if (email.contains('@') && password.length >= 6) {
      return {
        'access_token': 'mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
        'refresh_token': 'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
        'user': {
          'id': 'mock_user_id_${DateTime.now().millisecondsSinceEpoch}',
          'email': email,
          'name': name,
        },
      };
    } else {
      throw Exception('Invalid registration data');
    }
  }

  @override
  Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    await _simulateDelay();
    
    return {
      'access_token': 'new_mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
      'refresh_token': 'new_mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
    };
  }

  @override
  Future<Map<String, dynamic>> sendMessage({
    required String message,
    required String conversationId,
  }) async {
    await _simulateDelay();
    
    // Find or create conversation
    var conversation = _conversations.firstWhere(
      (c) => c['id'] == conversationId,
      orElse: () {
        final newConversation = {
          'id': conversationId,
          'messages': <Map<String, dynamic>>[],
          'created_at': DateTime.now().toIso8601String(),
        };
        _conversations.add(newConversation);
        return newConversation;
      },
    );

    // Add user message
    final userMessage = {
      'id': 'msg_${DateTime.now().millisecondsSinceEpoch}',
      'content': message,
      'role': 'user',
      'timestamp': DateTime.now().toIso8601String(),
    };
    conversation['messages'].add(userMessage);

    // Generate mock AI response
    final aiResponse = _generateMockResponse(message);
    final aiMessage = {
      'id': 'msg_${DateTime.now().millisecondsSinceEpoch + 1}',
      'content': aiResponse,
      'role': 'assistant',
      'timestamp': DateTime.now().toIso8601String(),
    };
    conversation['messages'].add(aiMessage);

    return aiMessage;
  }

  @override
  Future<List<Map<String, dynamic>>> getConversations() async {
    await _simulateDelay();
    return List<Map<String, dynamic>>.from(_conversations);
  }

  @override
  Future<Map<String, dynamic>> createTask({
    required String title,
    required String description,
  }) async {
    await _simulateDelay();
    
    final task = {
      'id': 'task_${_nextTaskId++}',
      'title': title,
      'description': description,
      'completed': false,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
    
    _tasks.add(task);
    return task;
  }

  @override
  Future<List<Map<String, dynamic>>> getTasks() async {
    await _simulateDelay();
    return List<Map<String, dynamic>>.from(_tasks);
  }

  @override
  Future<Map<String, dynamic>> updateTask({
    required String taskId,
    Map<String, dynamic>? updates,
  }) async {
    await _simulateDelay();
    
    final taskIndex = _tasks.indexWhere((task) => task['id'] == taskId);
    if (taskIndex == -1) {
      throw Exception('Task not found');
    }
    
    final task = _tasks[taskIndex];
    if (updates != null) {
      task.addAll(updates);
      task['updated_at'] = DateTime.now().toIso8601String();
    }
    
    return task;
  }

  @override
  Future<Map<String, dynamic>> createNote({
    required String title,
    required String content,
  }) async {
    await _simulateDelay();
    
    final note = {
      'id': 'note_${_nextNoteId++}',
      'title': title,
      'content': content,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
    
    _notes.add(note);
    return note;
  }

  @override
  Future<List<Map<String, dynamic>>> getNotes() async {
    await _simulateDelay();
    return List<Map<String, dynamic>>.from(_notes);
  }

  /// Generate a mock AI response based on the input message
  String _generateMockResponse(String message) {
    final lowerMessage = message.toLowerCase();
    
    if (lowerMessage.contains('hello') || lowerMessage.contains('hi')) {
      return 'Hello! How can I assist you today?';
    } else if (lowerMessage.contains('task') || lowerMessage.contains('todo')) {
      return 'I can help you manage your tasks. Would you like me to create a new task or show your existing ones?';
    } else if (lowerMessage.contains('note')) {
      return 'I can help you with notes. Would you like to create a new note or review your existing notes?';
    } else if (lowerMessage.contains('weather')) {
      return 'I don\'t have access to real-time weather data in this mock version, but I can help you with other tasks!';
    } else if (lowerMessage.contains('help')) {
      return 'I\'m here to help! I can assist with tasks, notes, conversations, and answer questions. What would you like to do?';
    } else {
      return 'That\'s an interesting question! In this mock version, I can help you with tasks, notes, and general conversation. How can I assist you further?';
    }
  }

  /// Add some sample data for testing
  void addSampleData() {
    // Sample tasks
    _tasks.addAll([
      {
        'id': 'task_1',
        'title': 'Review project proposal',
        'description': 'Go through the new project proposal and provide feedback',
        'completed': false,
        'created_at': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
        'updated_at': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
      },
      {
        'id': 'task_2',
        'title': 'Schedule team meeting',
        'description': 'Set up a meeting with the development team for next week',
        'completed': true,
        'created_at': DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
        'updated_at': DateTime.now().subtract(const Duration(hours: 3)).toIso8601String(),
      },
    ]);

    // Sample notes
    _notes.addAll([
      {
        'id': 'note_1',
        'title': 'Meeting Notes - Project Kickoff',
        'content': 'Key points from today\'s project kickoff meeting:\n- Timeline: 3 months\n- Team size: 5 developers\n- Budget approved',
        'created_at': DateTime.now().subtract(const Duration(hours: 4)).toIso8601String(),
        'updated_at': DateTime.now().subtract(const Duration(hours: 4)).toIso8601String(),
      },
    ]);

    _nextTaskId = 3;
    _nextNoteId = 2;
  }

  @override
  Future<Map<String, dynamic>> updateNote({
    required String noteId,
    Map<String, dynamic>? updates,
  }) async {
    await _simulateDelay();
    return {
      'id': noteId,
      'updated_at': DateTime.now().toIso8601String(),
      ...?updates,
    };
  }

  @override
  Future<Map<String, dynamic>> deleteNote(String noteId) async {
    await _simulateDelay();
    return {
      'id': noteId,
      'deleted': true,
      'deleted_at': DateTime.now().toIso8601String(),
    };
  }

  @override
  Future<Map<String, dynamic>> deleteTask(String taskId) async {
    await _simulateDelay();
    return {
      'id': taskId,
      'deleted': true,
      'deleted_at': DateTime.now().toIso8601String(),
    };
  }

  @override
  Future<Map<String, dynamic>> createCalendarEvent({
    required String title,
    required String description,
    required DateTime startTime,
    required DateTime endTime,
    String? type,
    bool? isAllDay,
    String? location,
    List<String>? attendees,
  }) async {
    await _simulateDelay();
    return {
      'id': 'event_${DateTime.now().millisecondsSinceEpoch}',
      'title': title,
      'description': description,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
      'type': type,
      'is_all_day': isAllDay,
      'location': location,
      'attendees': attendees ?? [],
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
  }

  @override
  Future<List<Map<String, dynamic>>> getCalendarEvents({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    await _simulateDelay();
    return [
      {
        'id': 'event_1',
        'title': 'Team Meeting',
        'description': 'Weekly team sync',
        'start_time': DateTime.now().add(const Duration(hours: 2)).toIso8601String(),
        'end_time': DateTime.now().add(const Duration(hours: 3)).toIso8601String(),
        'type': 'meeting',
        'is_all_day': false,
        'location': 'Conference Room A',
        'attendees': ['<EMAIL>', '<EMAIL>'],
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      },
    ];
  }

  @override
  Future<Map<String, dynamic>> updateCalendarEvent({
    required String eventId,
    Map<String, dynamic>? updates,
  }) async {
    await _simulateDelay();
    return {
      'id': eventId,
      'updated_at': DateTime.now().toIso8601String(),
      ...?updates,
    };
  }

  @override
  Future<Map<String, dynamic>> deleteCalendarEvent(String eventId) async {
    await _simulateDelay();
    return {
      'id': eventId,
      'deleted': true,
      'deleted_at': DateTime.now().toIso8601String(),
    };
  }

  @override
  Future<Map<String, dynamic>> searchNotes({
    required String query,
    String? tag,
    int page = 1,
    int limit = 20,
  }) async {
    await _simulateDelay();
    return {
      'results': [],
      'total': 0,
      'page': page,
      'limit': limit,
    };
  }

  @override
  Future<Map<String, dynamic>> searchTasks({
    required String query,
    String? status,
    String? priority,
    int page = 1,
    int limit = 20,
  }) async {
    await _simulateDelay();
    return {
      'results': [],
      'total': 0,
      'page': page,
      'limit': limit,
    };
  }

  @override
  Future<Map<String, dynamic>> searchCalendarEvents({
    required String query,
    String? type,
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int limit = 20,
  }) async {
    await _simulateDelay();
    return {
      'results': [],
      'total': 0,
      'page': page,
      'limit': limit,
    };
  }

  // --- Contact Management Mock Methods ---

  final List<Map<String, dynamic>> _contacts = [
    {
      'id': 'contact_1',
      'name': 'Sarah Johnson',
      'phone': '+****************',
      'email': '<EMAIL>',
      'location': 'Coffee Shop Downtown',
      'met_at': 'Coffee Shop Downtown',
      'social_media': {'instagram': 'sarah_j_photos'},
      'memory_prompt': 'Amazing photographer, loves travel',
      'image_path': null,
      'image_public_id': null,
      'device_sync_status': 'disabled',
      'created_at': DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
      'updated_at': DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
    },
    {
      'id': 'contact_2',
      'name': 'Mike Chen',
      'phone': '+****************',
      'email': '<EMAIL>',
      'location': 'Tech Conference 2024',
      'met_at': 'Tech Conference 2024',
      'social_media': {'linkedin': 'mike-chen-dev'},
      'memory_prompt': 'Senior developer at startup, interested in AI',
      'image_path': null,
      'image_public_id': null,
      'device_sync_status': 'synced',
      'created_at': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
      'updated_at': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
    },
  ];

  int _nextContactId = 3;

  @override
  Future<Map<String, dynamic>> createContact({
    required String name,
    required String phone,
    String? email,
    String? location,
    String? metAt,
    Map<String, String>? socialMedia,
    String? memoryPrompt,
    String? imagePath,
    String? imagePublicId,
    String deviceSyncStatus = 'disabled',
  }) async {
    await _simulateDelay();

    final contact = {
      'id': 'contact_${_nextContactId++}',
      'name': name,
      'phone': phone,
      'email': email,
      'location': location,
      'met_at': metAt,
      'social_media': socialMedia ?? {},
      'memory_prompt': memoryPrompt,
      'image_path': imagePath,
      'image_public_id': imagePublicId,
      'device_sync_status': deviceSyncStatus,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };

    _contacts.insert(0, contact);
    return contact;
  }

  @override
  Future<Map<String, dynamic>> getContacts({
    String? search,
    String? platform,
    int limit = 50,
    int offset = 0,
    String sortBy = 'name',
    String sortOrder = 'asc',
  }) async {
    await _simulateDelay();

    var filteredContacts = List<Map<String, dynamic>>.from(_contacts);

    if (search != null && search.isNotEmpty) {
      filteredContacts = filteredContacts.where((contact) {
        final name = contact['name']?.toString().toLowerCase() ?? '';
        final phone = contact['phone']?.toString().toLowerCase() ?? '';
        final email = contact['email']?.toString().toLowerCase() ?? '';
        final location = contact['location']?.toString().toLowerCase() ?? '';
        final searchLower = search.toLowerCase();

        return name.contains(searchLower) ||
               phone.contains(searchLower) ||
               email.contains(searchLower) ||
               location.contains(searchLower);
      }).toList();
    }

    if (platform != null && platform.isNotEmpty) {
      filteredContacts = filteredContacts.where((contact) {
        final socialMedia = contact['social_media'] as Map<String, dynamic>? ?? {};
        return socialMedia.containsKey(platform);
      }).toList();
    }

    // Apply sorting
    filteredContacts.sort((a, b) {
      final aValue = a[sortBy]?.toString() ?? '';
      final bValue = b[sortBy]?.toString() ?? '';
      return sortOrder == 'asc' ? aValue.compareTo(bValue) : bValue.compareTo(aValue);
    });

    // Apply pagination
    final startIndex = offset;
    final endIndex = (offset + limit).clamp(0, filteredContacts.length);
    final paginatedContacts = filteredContacts.sublist(startIndex, endIndex);

    return {
      'contacts': paginatedContacts,
      'total': filteredContacts.length,
      'limit': limit,
      'offset': offset,
    };
  }

  @override
  Future<Map<String, dynamic>> getContact(String contactId) async {
    await _simulateDelay();

    final contact = _contacts.firstWhere(
      (c) => c['id'] == contactId,
      orElse: () => throw Exception('Contact not found'),
    );

    return contact;
  }

  @override
  Future<Map<String, dynamic>> updateContact({
    required String contactId,
    String? name,
    String? phone,
    String? email,
    String? location,
    String? metAt,
    Map<String, String>? socialMedia,
    String? memoryPrompt,
    String? imagePath,
    String? imagePublicId,
    String? deviceSyncStatus,
  }) async {
    await _simulateDelay();

    final contactIndex = _contacts.indexWhere((c) => c['id'] == contactId);
    if (contactIndex == -1) {
      throw Exception('Contact not found');
    }

    final contact = _contacts[contactIndex];

    if (name != null) contact['name'] = name;
    if (phone != null) contact['phone'] = phone;
    if (email != null) contact['email'] = email;
    if (location != null) contact['location'] = location;
    if (metAt != null) contact['met_at'] = metAt;
    if (socialMedia != null) contact['social_media'] = socialMedia;
    if (memoryPrompt != null) contact['memory_prompt'] = memoryPrompt;
    if (imagePath != null) contact['image_path'] = imagePath;
    if (imagePublicId != null) contact['image_public_id'] = imagePublicId;
    if (deviceSyncStatus != null) contact['device_sync_status'] = deviceSyncStatus;

    contact['updated_at'] = DateTime.now().toIso8601String();

    return contact;
  }

  @override
  Future<Map<String, dynamic>> deleteContact(String contactId) async {
    await _simulateDelay();

    final contactIndex = _contacts.indexWhere((c) => c['id'] == contactId);
    if (contactIndex == -1) {
      throw Exception('Contact not found');
    }

    _contacts.removeAt(contactIndex);

    return {
      'id': contactId,
      'deleted': true,
      'deleted_at': DateTime.now().toIso8601String(),
    };
  }

  @override
  Future<String> getContactVCF(String contactId) async {
    await _simulateDelay();

    final contact = _contacts.firstWhere(
      (c) => c['id'] == contactId,
      orElse: () => throw Exception('Contact not found'),
    );

    // Generate mock VCF content
    final vcfContent = '''BEGIN:VCARD
VERSION:3.0
N:${contact['name']?.split(' ').reversed.join(';') ?? ''};;;
FN:${contact['name'] ?? ''}
TEL;TYPE=CELL:${contact['phone'] ?? ''}
EMAIL;TYPE=HOME:${contact['email'] ?? ''}
ORG:Met at: ${contact['met_at'] ?? ''}
NOTE:${contact['memory_prompt'] ?? ''}
REV:${DateTime.now().toIso8601String()}
END:VCARD''';

    return vcfContent;
  }

  @override
  Future<Map<String, dynamic>> bulkExportContacts({
    required List<String> contactIds,
    String format = 'vcf',
    bool includeImages = false,
  }) async {
    await _simulateDelay();

    return {
      'export_id': 'export_${DateTime.now().millisecondsSinceEpoch}',
      'format': format,
      'contact_count': contactIds.length,
      'download_url': 'https://mock.api.com/exports/contacts.vcf',
      'expires_at': DateTime.now().add(const Duration(hours: 24)).toIso8601String(),
    };
  }

  @override
  Future<Map<String, dynamic>> exportAllContacts({
    String format = 'vcf',
    bool includeImages = false,
  }) async {
    await _simulateDelay();

    return {
      'export_id': 'export_all_${DateTime.now().millisecondsSinceEpoch}',
      'format': format,
      'contact_count': _contacts.length,
      'download_url': 'https://mock.api.com/exports/all_contacts.vcf',
      'expires_at': DateTime.now().add(const Duration(hours: 24)).toIso8601String(),
    };
  }

  @override
  Future<Map<String, dynamic>> updateDeviceSync({
    required String contactId,
    String? deviceContactId,
    required String syncStatus,
    String? syncError,
    Map<String, String>? deviceInfo,
  }) async {
    await _simulateDelay();

    final contactIndex = _contacts.indexWhere((c) => c['id'] == contactId);
    if (contactIndex != -1) {
      _contacts[contactIndex]['device_sync_status'] = syncStatus;
      _contacts[contactIndex]['updated_at'] = DateTime.now().toIso8601String();
    }

    return {
      'contact_id': contactId,
      'sync_status': syncStatus,
      'device_contact_id': deviceContactId,
      'sync_error': syncError,
      'updated_at': DateTime.now().toIso8601String(),
    };
  }

  @override
  Future<Map<String, dynamic>> getContactsSyncStatus() async {
    await _simulateDelay();

    final syncStatuses = _contacts.map((contact) => {
      'contact_id': contact['id'],
      'sync_status': contact['device_sync_status'],
      'last_sync_attempt': contact['updated_at'],
      'sync_error': null,
    }).toList();

    final syncedCount = _contacts.where((c) => c['device_sync_status'] == 'synced').length;
    final pendingCount = _contacts.where((c) => c['device_sync_status'] == 'pending').length;
    final failedCount = _contacts.where((c) => c['device_sync_status'] == 'failed').length;

    return {
      'contacts': syncStatuses,
      'summary': {
        'total': _contacts.length,
        'synced': syncedCount,
        'pending': pendingCount,
        'failed': failedCount,
      },
    };
  }

  @override
  Future<Map<String, dynamic>> bulkDeviceSync({
    required List<String> contactIds,
    required Map<String, String> deviceInfo,
  }) async {
    await _simulateDelay();

    final results = contactIds.map((id) => {
      'contact_id': id,
      'sync_status': 'synced',
      'device_contact_id': 'device_${id}_${DateTime.now().millisecondsSinceEpoch}',
    }).toList();

    return {
      'bulk_sync_id': 'bulk_${DateTime.now().millisecondsSinceEpoch}',
      'results': results,
      'success_count': contactIds.length,
      'failure_count': 0,
    };
  }

  @override
  Future<Map<String, dynamic>> searchContacts({
    required String query,
    String? fields,
    String? platform,
    String? dateFrom,
    String? dateTo,
    int limit = 20,
    int offset = 0,
  }) async {
    await _simulateDelay();

    var results = List<Map<String, dynamic>>.from(_contacts);

    // Apply search filter
    results = results.where((contact) {
      final searchFields = fields?.split(',') ?? ['name', 'phone', 'email', 'location'];
      final queryLower = query.toLowerCase();

      return searchFields.any((field) {
        final value = contact[field]?.toString().toLowerCase() ?? '';
        return value.contains(queryLower);
      });
    }).toList();

    // Apply platform filter
    if (platform != null) {
      results = results.where((contact) {
        final socialMedia = contact['social_media'] as Map<String, dynamic>? ?? {};
        return socialMedia.containsKey(platform);
      }).toList();
    }

    // Apply pagination
    final startIndex = offset;
    final endIndex = (offset + limit).clamp(0, results.length);
    final paginatedResults = results.sublist(startIndex, endIndex);

    return {
      'results': paginatedResults,
      'total': results.length,
      'limit': limit,
      'offset': offset,
      'query': query,
    };
  }

  @override
  Future<Map<String, dynamic>> getContactAnalytics() async {
    await _simulateDelay();

    final totalContacts = _contacts.length;
    final thisMonth = DateTime.now().subtract(const Duration(days: 30));
    final contactsThisMonth = _contacts.where((c) {
      final createdAt = DateTime.parse(c['created_at']);
      return createdAt.isAfter(thisMonth);
    }).length;

    // Calculate top social platforms
    final platformCounts = <String, int>{};
    for (final contact in _contacts) {
      final socialMedia = contact['social_media'] as Map<String, dynamic>? ?? {};
      for (final platform in socialMedia.keys) {
        platformCounts[platform] = (platformCounts[platform] ?? 0) + 1;
      }
    }

    final topPlatforms = platformCounts.entries
        .map((e) => {'platform': e.key, 'count': e.value})
        .toList()
      ..sort((a, b) => (b['count'] as int).compareTo(a['count'] as int));

    // Calculate top locations
    final locationCounts = <String, int>{};
    for (final contact in _contacts) {
      final location = contact['met_at']?.toString();
      if (location != null && location.isNotEmpty) {
        locationCounts[location] = (locationCounts[location] ?? 0) + 1;
      }
    }

    final topLocations = locationCounts.entries
        .map((e) => {'location': e.key, 'count': e.value})
        .toList()
      ..sort((a, b) => (b['count'] as int).compareTo(a['count'] as int));

    return {
      'total_contacts': totalContacts,
      'contacts_this_month': contactsThisMonth,
      'top_social_platforms': topPlatforms.take(5).toList(),
      'top_locations': topLocations.take(5).toList(),
      'device_sync_stats': {
        'synced': _contacts.where((c) => c['device_sync_status'] == 'synced').length,
        'pending': _contacts.where((c) => c['device_sync_status'] == 'pending').length,
        'failed': _contacts.where((c) => c['device_sync_status'] == 'failed').length,
      },
    };
  }

  @override
  Future<Map<String, dynamic>> getSocialPlatforms() async {
    await _simulateDelay();

    return {
      'platforms': [
        {
          'name': 'twitter',
          'display_name': 'Twitter',
          'url_pattern': 'https://twitter.com/{username}',
          'app_scheme': 'twitter://user?screen_name={username}',
          'username_validation': r'^[a-zA-Z0-9_]{1,15}$',
          'username_placeholder': '@username',
          'color': '#1DA1F2',
          'icon_url': 'https://mock.api.com/icons/twitter.png',
          'is_active': true,
        },
        {
          'name': 'instagram',
          'display_name': 'Instagram',
          'url_pattern': 'https://instagram.com/{username}',
          'app_scheme': 'instagram://user?username={username}',
          'username_validation': r'^[a-zA-Z0-9_.]{1,30}$',
          'username_placeholder': '@username',
          'color': '#E4405F',
          'icon_url': 'https://mock.api.com/icons/instagram.png',
          'is_active': true,
        },
        {
          'name': 'linkedin',
          'display_name': 'LinkedIn',
          'url_pattern': 'https://linkedin.com/in/{username}',
          'app_scheme': 'linkedin://profile/{username}',
          'username_validation': r'^[a-zA-Z0-9-]+$',
          'username_placeholder': 'profile-name',
          'color': '#0077B5',
          'icon_url': 'https://mock.api.com/icons/linkedin.png',
          'is_active': true,
        },
      ],
    };
  }

  @override
  Future<Map<String, dynamic>> getSocialPlatform(String platform) async {
    await _simulateDelay();

    final platforms = await getSocialPlatforms();
    final platformData = (platforms['platforms'] as List).firstWhere(
      (p) => p['name'] == platform,
      orElse: () => throw Exception('Platform not found'),
    );

    return platformData;
  }

  @override
  Future<Map<String, dynamic>> validateSocialUsername({
    required String platform,
    required String username,
  }) async {
    await _simulateDelay();

    final platformData = await getSocialPlatform(platform);
    final validation = platformData['username_validation'] as String;
    final regex = RegExp(validation);

    return {
      'platform': platform,
      'username': username,
      'is_valid': regex.hasMatch(username),
      'validation_pattern': validation,
    };
  }

  @override
  Future<Map<String, dynamic>> verifySocialProfile({
    required String contactId,
    required String platform,
    required String username,
  }) async {
    await _simulateDelay();

    // Mock verification - assume most profiles exist
    final exists = username.length > 3; // Simple mock logic

    return {
      'verified': exists,
      'profile_exists': exists,
      'profile_url': 'https://$platform.com/$username',
      'profile_info': exists ? {
        'display_name': username.replaceAll('_', ' ').toUpperCase(),
        'verified': false,
        'follower_count': 1250,
      } : null,
    };
  }

  // --- Smart Capture (Frictionless Inbox) Mock Methods ---

  @override
  Future<Map<String, dynamic>> captureContent({
    required String url,
    String? title,
    String? description,
    List<String>? tags,
  }) async {
    await Future.delayed(const Duration(milliseconds: 1500));
    return {
      'id': 'mock_${DateTime.now().millisecondsSinceEpoch}',
      'url': url,
      'title': title ?? 'Mock Captured Content',
      'summary': 'This is a mock summary for the captured content.',
      'content_type': 'link',
      'processing_status': 'completed',
      'created_at': DateTime.now().toIso8601String(),
      'tags': tags ?? ['mock'],
      'favicon_url': 'https://example.com/favicon.ico',
    };
  }

  @override
  Future<Map<String, dynamic>> uploadAndProcessImage(File image) async {
    await Future.delayed(const Duration(milliseconds: 2000));
    return {
      'id': 'mock_img_${DateTime.now().millisecondsSinceEpoch}',
      'url': image.path,
      'title': 'Mock Processed Image',
      'summary': 'This is a mock summary for the processed image content.',
      'content_type': 'image',
      'processing_status': 'completed',
      'created_at': DateTime.now().toIso8601String(),
      'tags': ['image', 'mock'],
      'thumbnail_url': image.path,
    };
  }

  @override
  Future<Map<String, dynamic>> getCapturedContent({
    String? search,
    String? contentType,
    String? status,
    List<String>? tags,
    int limit = 50,
    int offset = 0,
    String sortBy = 'created_at',
    String sortOrder = 'desc',
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return {
      'items': [
        {
          'id': 'mock_1',
          'url': 'https://example.com/article1',
          'title': 'Mock Article 1',
          'summary': 'This is a mock summary for article 1.',
          'content_type': 'link',
          'processing_status': 'completed',
          'created_at': DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
          'tags': ['mock', 'article'],
        },
        {
          'id': 'mock_2',
          'url': 'https://example.com/article2',
          'title': 'Mock Article 2',
          'summary': 'This is a mock summary for article 2.',
          'content_type': 'link',
          'processing_status': 'completed',
          'created_at': DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
          'tags': ['mock', 'news'],
        },
      ],
      'total': 2,
      'limit': limit,
      'offset': offset,
    };
  }

  @override
  Future<Map<String, dynamic>> updateCapturedContent({
    required String id,
    String? title,
    String? summary,
    List<String>? tags,
    String? status,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return {
      'id': id,
      'title': title ?? 'Updated Mock Title',
      'summary': summary ?? 'Updated mock summary.',
      'tags': tags ?? ['updated'],
      'updated_at': DateTime.now().toIso8601String(),
    };
  }

  @override
  Future<Map<String, dynamic>> deleteCapturedContent(String id) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return {
      'success': true,
      'deleted_id': id,
    };
  }

  @override
  Future<Map<String, dynamic>> bulkDeleteCapturedContent(List<String> ids) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return {
      'success': true,
      'deleted_count': ids.length,
      'deleted_ids': ids,
    };
  }

  @override
  Future<Map<String, dynamic>> searchCapturedContent({
    required String query,
    String? contentType,
    List<String>? tags,
    String? dateFrom,
    String? dateTo,
    int limit = 20,
    int offset = 0,
  }) async {
    await Future.delayed(const Duration(milliseconds: 800));
    return {
      'items': [
        {
          'id': 'search_result_1',
          'url': 'https://example.com/search-result',
          'title': 'Search Result for: $query',
          'summary': 'This is a mock search result summary.',
          'content_type': contentType ?? 'link',
          'processing_status': 'completed',
          'created_at': DateTime.now().toIso8601String(),
          'tags': tags ?? ['search', 'mock'],
        },
      ],
      'total': 1,
      'query': query,
      'limit': limit,
      'offset': offset,
    };
  }

  @override
  Future<Map<String, dynamic>> getProcessingStatus(String contentId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return {
      'content_id': contentId,
      'status': 'completed',
      'progress': 100,
      'message': 'Processing completed successfully',
      'updated_at': DateTime.now().toIso8601String(),
    };
  }

  @override
  Future<Map<String, dynamic>> getCapturedContentTags() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return {
      'tags': ['article', 'news', 'tutorial', 'documentation', 'video', 'image', 'mock'],
    };
  }

  @override
  Future<Map<String, dynamic>> importContentToNotes({
    required String contentId,
    String? noteTitle,
    String? noteContent,
    List<String>? tags,
  }) async {
    await Future.delayed(const Duration(milliseconds: 600));
    return {
      'success': true,
      'note_id': 'note_${DateTime.now().millisecondsSinceEpoch}',
      'content_id': contentId,
      'title': noteTitle ?? 'Imported Content',
    };
  }

  @override
  Future<Map<String, dynamic>> addContentToChatContext({
    required String contentId,
    String? conversationId,
    String? contextMessage,
  }) async {
    await Future.delayed(const Duration(milliseconds: 400));
    return {
      'success': true,
      'content_id': contentId,
      'conversation_id': conversationId ?? 'conv_${DateTime.now().millisecondsSinceEpoch}',
      'context_added': true,
    };
  }

  @override
  Future<Map<String, dynamic>> createEventFromContent({
    required String contentId,
    required String title,
    required DateTime startTime,
    required DateTime endTime,
    String? description,
    String? location,
    List<String>? attendees,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return {
      'success': true,
      'event_id': 'event_${DateTime.now().millisecondsSinceEpoch}',
      'content_id': contentId,
      'title': title,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
    };
  }

  // Add missing methods
  @override
  Future<Map<String, dynamic>> getCapturedContentById(String id) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return {
      'id': id,
      'url': 'https://example.com/content',
      'title': 'Mock Content by ID',
      'summary': 'This is a mock content retrieved by ID.',
      'content_type': 'link',
      'processing_status': 'completed',
      'created_at': DateTime.now().toIso8601String(),
      'tags': ['mock'],
    };
  }

  @override
  Future<Map<String, dynamic>> getCapturedContentAnalytics({
    String? dateFrom,
    String? dateTo,
    String? groupBy,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return {
      'total_content': 42,
      'content_by_type': {
        'link': 30,
        'image': 8,
        'text': 4,
      },
      'content_by_status': {
        'completed': 38,
        'processing': 2,
        'error': 2,
      },
      'daily_stats': [
        {'date': '2024-01-01', 'count': 5},
        {'date': '2024-01-02', 'count': 8},
        {'date': '2024-01-03', 'count': 12},
      ],
    };
  }

  @override
  Future<Map<String, dynamic>> retryContentProcessing(String contentId) async {
    await Future.delayed(const Duration(milliseconds: 800));
    return {
      'content_id': contentId,
      'status': 'processing',
      'message': 'Content processing restarted',
      'retry_count': 1,
    };
  }

  @override
  Future<Map<String, dynamic>> createConversation({
    required String title,
    String? description,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final conversationId = 'conv_${_nextConversationId++}';
    final timestamp = DateTime.now().toIso8601String();

    final conversation = {
      'id': conversationId,
      'title': title,
      'description': description ?? '',
      'created_at': timestamp,
      'updated_at': timestamp,
      'messages': <Map<String, dynamic>>[],
    };

    _conversations.add(conversation);

    return conversation;
  }

  @override
  Future<void> deleteConversation(String conversationId) async {
    await Future.delayed(const Duration(milliseconds: 200));

    final conversationIndex = _conversations.indexWhere((c) => c['id'] == conversationId);
    if (conversationIndex != -1) {
      _conversations.removeAt(conversationIndex);
    }
  }
}
