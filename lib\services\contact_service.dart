import 'package:isar/isar.dart';
import '../models/contact_models.dart';
import 'api_service.dart';
import 'sync_engine.dart';
import 'vcf_service.dart';
import 'device_contacts_service.dart';
import 'social_media_service.dart';
import 'data_service.dart' as data_service;

/// Service for contact management with offline-first approach
class ContactService {
  final Isar _isar;
  final ApiService _apiService;
  final SyncEngine _syncEngine;
  final VcfService _vcfService;
  final DeviceContactsService _deviceContactsService;
  final SocialMediaService _socialMediaService;

  ContactService({
    required Isar isar,
    required ApiService apiService,
    required SyncEngine syncEngine,
    required VcfService vcfService,
    required DeviceContactsService deviceContactsService,
    required SocialMediaService socialMediaService,
  })  : _isar = isar,
        _apiService = apiService,
        _syncEngine = syncEngine,
        _vcfService = vcfService,
        _deviceContactsService = deviceContactsService,
        _socialMediaService = socialMediaService;

  /// Get all contacts from local database
  Future<List<ContactModel>> getAllContacts() async {
    try {
      return await _isar.contactModels
          .where()
          .sortByName()
          .findAll();
    } catch (e) {
      print('❌ Failed to get contacts: $e');
      return [];
    }
  }

  /// Search contacts by name, email, or phone
  Future<List<ContactModel>> searchContacts(String query) async {
    if (query.isEmpty) return getAllContacts();

    try {
      final lowerQuery = query.toLowerCase();
      return await _isar.contactModels
          .filter()
          .group((q) => q
              .nameContains(lowerQuery, caseSensitive: false)
              .or()
              .emailContains(lowerQuery, caseSensitive: false)
              .or()
              .phoneContains(lowerQuery, caseSensitive: false)
              .or()
              .companyContains(lowerQuery, caseSensitive: false))
          .sortByName()
          .findAll();
    } catch (e) {
      print('❌ Failed to search contacts: $e');
      return [];
    }
  }

  /// Create new contact
  Future<ContactModel> createContact(ContactModel contact) async {
    try {
      // Set timestamps
      contact.createdAt = DateTime.now();
      contact.updatedAt = DateTime.now();
      contact.markForSync();

      // Save to local database
      await _isar.writeTxn(() async {
        await _isar.contactModels.put(contact);
      });

      // Queue for sync
      await _syncEngine.queueCreateOperation(
        entityType: 'contact',
        entityId: contact.contactId,
        data: contact.toApi(),
      );

      print('✅ Contact created successfully: ${contact.name}');
      return contact;
    } catch (e) {
      print('❌ Failed to create contact: $e');
      throw Exception('Failed to create contact: $e');
    }
  }

  /// Update existing contact
  Future<ContactModel> updateContact(ContactModel contact) async {
    try {
      // Update timestamp
      contact.updatedAt = DateTime.now();
      contact.markForSync();

      // Save to local database
      await _isar.writeTxn(() async {
        await _isar.contactModels.put(contact);
      });

      // Queue for sync
      await _syncEngine.queueUpdateOperation(
        entityType: 'contact',
        entityId: contact.contactId,
        data: contact.toApi(),
      );

      print('✅ Contact updated successfully: ${contact.name}');
      return contact;
    } catch (e) {
      print('❌ Failed to update contact: $e');
      throw Exception('Failed to update contact: $e');
    }
  }

  /// Delete contact
  Future<bool> deleteContact(String contactId) async {
    try {
      final contact = await _isar.contactModels
          .filter()
          .contactIdEqualTo(contactId)
          .findFirst();

      if (contact == null) {
        throw Exception('Contact not found');
      }

      // Delete from local database
      await _isar.writeTxn(() async {
        await _isar.contactModels.delete(contact.id);
      });

      // Queue for sync
      await _syncEngine.queueDeleteOperation(
        entityType: 'contact',
        entityId: contactId,
      );

      print('✅ Contact deleted successfully: $contactId');
      return true;
    } catch (e) {
      print('❌ Failed to delete contact: $e');
      return false;
    }
  }

  /// Bulk delete contacts
  Future<int> bulkDeleteContacts(List<String> contactIds) async {
    int deletedCount = 0;
    
    try {
      for (final contactId in contactIds) {
        final success = await deleteContact(contactId);
        if (success) deletedCount++;
      }

      print('✅ Bulk deleted $deletedCount contacts');
      return deletedCount;
    } catch (e) {
      print('❌ Failed to bulk delete contacts: $e');
      return deletedCount;
    }
  }

  /// Export contact as VCF
  Future<String> exportContactAsVcf(ContactModel contact) async {
    try {
      // Convert ContactModel to Contact for VcfService
      final dataServiceContact = data_service.Contact(
        id: contact.contactId ?? '',
        name: '${contact.firstName} ${contact.lastName}'.trim(),
        phone: contact.phone ?? '',
        email: contact.email ?? '',
        location: contact.company ?? '',
        memoryPrompt: contact.notes ?? '',
        socialMedia: contact.getSocialMediaProfiles(),
        createdAt: contact.createdAt,
        updatedAt: contact.updatedAt,
      );

      return await _vcfService.saveVcfFile(dataServiceContact);
    } catch (e) {
      print('❌ Failed to export contact as VCF: $e');
      throw Exception('Failed to export contact: $e');
    }
  }

  /// Export multiple contacts as VCF
  Future<String> exportContactsAsVcf(List<ContactModel> contacts) async {
    try {
      // Export each contact individually and combine
      final vcfContents = <String>[];

      for (final contact in contacts) {
        final dataServiceContact = data_service.Contact(
          id: contact.contactId ?? '',
          name: '${contact.firstName} ${contact.lastName}'.trim(),
          phone: contact.phone ?? '',
          email: contact.email ?? '',
          location: contact.company ?? '',
          memoryPrompt: contact.notes ?? '',
          socialMedia: contact.getSocialMediaProfiles(),
          createdAt: contact.createdAt,
          updatedAt: contact.updatedAt,
        );

        final vcfContent = await _vcfService.generateVcfContent(dataServiceContact);
        vcfContents.add(vcfContent);
      }

      // Save combined VCF content to file
      final combinedVcf = vcfContents.join('\n\n');
      // For now, just return the content. In a real implementation,
      // you'd save it to a file and return the file path
      return combinedVcf;
    } catch (e) {
      print('❌ Failed to export contacts as VCF: $e');
      throw Exception('Failed to export contacts: $e');
    }
  }

  /// Import contacts from device
  Future<Map<String, int>> importContactsFromDevice() async {
    try {
      final deviceContacts = await _deviceContactsService.getDeviceContacts();
      int importedCount = 0;
      int skippedCount = 0;

      for (final deviceContact in deviceContacts) {
        // Check if contact already exists
        final existing = await _isar.contactModels
            .filter()
            .nameEqualTo(deviceContact.name)
            .findFirst();

        if (existing != null) {
          skippedCount++;
          continue;
        }

        // Create new contact from device contact
        final nameParts = deviceContact.name.split(' ');
        final contact = ContactModel()
          ..name = deviceContact.name
          ..firstName = nameParts.isNotEmpty ? nameParts.first : ''
          ..lastName = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : ''
          ..email = deviceContact.email
          ..phone = deviceContact.phone
          ..company = deviceContact.location
          ..notes = deviceContact.memoryPrompt
          ..source = 'device'
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();

        await createContact(contact);
        importedCount++;
      }

      print('✅ Imported $importedCount contacts, skipped $skippedCount');
      return {
        'imported': importedCount,
        'skipped': skippedCount,
      };
    } catch (e) {
      print('❌ Failed to import contacts from device: $e');
      throw Exception('Failed to import contacts: $e');
    }
  }

  /// Sync contacts with backend
  Future<int> syncContacts() async {
    try {
      // Get contacts that need sync
      final contactsToSync = await _isar.contactModels
          .filter()
          .needsSyncEqualTo(true)
          .findAll();

      int syncedCount = 0;

      for (final contact in contactsToSync) {
        try {
          if (contact.contactId.isEmpty) {
            // Create new contact on backend
            final response = await _apiService.createContact(
              name: contact.name,
              phone: contact.phone ?? '',
              email: contact.email ?? '',
              location: contact.company ?? '',
              memoryPrompt: contact.notes ?? '',
              socialMedia: contact.getSocialMediaProfiles(),
            );
            contact.markAsSynced(response['id']?.toString() ?? '');
          } else {
            // Update existing contact on backend
            await _apiService.updateContact(
              contactId: contact.contactId ?? '',
              name: contact.name,
              phone: contact.phone ?? '',
              email: contact.email ?? '',
              location: contact.company ?? '',
              memoryPrompt: contact.notes ?? '',
              socialMedia: contact.getSocialMediaProfiles(),
            );
            contact.markAsSynced(contact.contactId);
          }

          // Update local record
          await _isar.writeTxn(() async {
            await _isar.contactModels.put(contact);
          });

          syncedCount++;
        } catch (e) {
          print('❌ Failed to sync contact ${contact.name}: $e');
          contact.markSyncFailed();
          await _isar.writeTxn(() async {
            await _isar.contactModels.put(contact);
          });
        }
      }

      print('✅ Synced $syncedCount contacts');
      return syncedCount;
    } catch (e) {
      print('❌ Failed to sync contacts: $e');
      return 0;
    }
  }

  /// Add social media profile to contact
  Future<bool> addSocialMediaProfile({
    required String contactId,
    required String platform,
    required String username,
  }) async {
    try {
      final contact = await _isar.contactModels
          .filter()
          .contactIdEqualTo(contactId)
          .findFirst();

      if (contact == null) {
        throw Exception('Contact not found');
      }

      final profiles = contact.getSocialMediaProfiles();
      profiles[platform] = username;
      contact.setSocialMediaProfiles(profiles);

      await updateContact(contact);
      return true;
    } catch (e) {
      print('❌ Failed to add social media profile: $e');
      return false;
    }
  }

  /// Remove social media profile from contact
  Future<bool> removeSocialMediaProfile({
    required String contactId,
    required String platform,
  }) async {
    try {
      final contact = await _isar.contactModels
          .filter()
          .contactIdEqualTo(contactId)
          .findFirst();

      if (contact == null) {
        throw Exception('Contact not found');
      }

      final profiles = contact.getSocialMediaProfiles();
      profiles.remove(platform);
      contact.setSocialMediaProfiles(profiles);

      await updateContact(contact);
      return true;
    } catch (e) {
      print('❌ Failed to remove social media profile: $e');
      return false;
    }
  }

  /// Open social media profile
  Future<bool> openSocialMediaProfile({
    required String platform,
    required String username,
  }) async {
    try {
      return await _socialMediaService.openProfile(platform, username);
    } catch (e) {
      print('❌ Failed to open social media profile: $e');
      return false;
    }
  }

  /// Get contacts by tag
  Future<List<ContactModel>> getContactsByTag(String tag) async {
    try {
      return await _isar.contactModels
          .filter()
          .tagsContains(tag, caseSensitive: false)
          .sortByName()
          .findAll();
    } catch (e) {
      print('❌ Failed to get contacts by tag: $e');
      return [];
    }
  }

  /// Get all unique tags
  Future<List<String>> getAllTags() async {
    try {
      final contacts = await _isar.contactModels.where().findAll();
      final allTags = <String>{};
      
      for (final contact in contacts) {
        allTags.addAll(contact.getTagsList());
      }
      
      return allTags.toList()..sort();
    } catch (e) {
      print('❌ Failed to get all tags: $e');
      return [];
    }
  }
}
