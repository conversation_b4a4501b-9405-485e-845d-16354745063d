import 'package:isar/isar.dart';

part 'contact_models.g.dart';

/// Contact model for Isar database
@collection
class ContactModel {
  Id id = Isar.autoIncrement;

  @Index()
  late String contactId; // Backend ID

  @Index()
  late String name;

  String? firstName;
  String? lastName;
  String? company;
  String? jobTitle;
  String? email;
  String? phone;
  String? address;
  String? notes;
  String? profilePictureUrl;

  /// Social media profiles as JSON string
  String? socialMediaProfiles;

  /// Tags as comma-separated string
  String? tags;

  /// Contact source (device, manual, imported)
  String? source;

  /// Last contacted timestamp
  DateTime? lastContacted;

  /// Contact frequency (how often contacted)
  int contactFrequency = 0;

  /// Favorite contact flag
  bool isFavorite = false;

  /// Sync status with backend
  String syncStatus = 'pending'; // pending, synced, error

  /// Creation and update timestamps
  late DateTime createdAt;
  late DateTime updatedAt;

  /// Needs sync flag
  bool needsSync = true;

  ContactModel();

  /// Create from API response
  factory ContactModel.fromApi(Map<String, dynamic> json) {
    final contact = ContactModel()
      ..contactId = json['id']?.toString() ?? ''
      ..name = json['name']?.toString() ?? ''
      ..firstName = json['first_name']?.toString()
      ..lastName = json['last_name']?.toString()
      ..company = json['company']?.toString()
      ..jobTitle = json['job_title']?.toString()
      ..email = json['email']?.toString()
      ..phone = json['phone']?.toString()
      ..address = json['address']?.toString()
      ..notes = json['notes']?.toString()
      ..profilePictureUrl = json['profile_picture_url']?.toString()
      ..socialMediaProfiles = json['social_media_profiles']?.toString()
      ..tags = json['tags']?.toString()
      ..source = json['source']?.toString() ?? 'manual'
      ..contactFrequency = json['contact_frequency']?.toInt() ?? 0
      ..isFavorite = json['is_favorite'] == true
      ..syncStatus = 'synced'
      ..needsSync = false;

    // Parse timestamps
    if (json['last_contacted'] != null) {
      contact.lastContacted = DateTime.parse(json['last_contacted']);
    }
    if (json['created_at'] != null) {
      contact.createdAt = DateTime.parse(json['created_at']);
    } else {
      contact.createdAt = DateTime.now();
    }
    if (json['updated_at'] != null) {
      contact.updatedAt = DateTime.parse(json['updated_at']);
    } else {
      contact.updatedAt = DateTime.now();
    }

    return contact;
  }

  /// Convert to API format
  Map<String, dynamic> toApi() {
    return {
      'id': contactId.isEmpty ? null : contactId,
      'name': name,
      'first_name': firstName,
      'last_name': lastName,
      'company': company,
      'job_title': jobTitle,
      'email': email,
      'phone': phone,
      'address': address,
      'notes': notes,
      'profile_picture_url': profilePictureUrl,
      'social_media_profiles': socialMediaProfiles,
      'tags': tags,
      'source': source,
      'last_contacted': lastContacted?.toIso8601String(),
      'contact_frequency': contactFrequency,
      'is_favorite': isFavorite,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Get social media profiles as map
  Map<String, String> getSocialMediaProfiles() {
    if (socialMediaProfiles == null || socialMediaProfiles!.isEmpty) {
      return {};
    }
    try {
      // Parse JSON string to map
      final profiles = <String, String>{};
      final parts = socialMediaProfiles!.split(',');
      for (final part in parts) {
        final keyValue = part.split(':');
        if (keyValue.length == 2) {
          profiles[keyValue[0].trim()] = keyValue[1].trim();
        }
      }
      return profiles;
    } catch (e) {
      return {};
    }
  }

  /// Set social media profiles from map
  void setSocialMediaProfiles(Map<String, String> profiles) {
    if (profiles.isEmpty) {
      socialMediaProfiles = null;
      return;
    }
    socialMediaProfiles = profiles.entries
        .map((e) => '${e.key}:${e.value}')
        .join(',');
  }

  /// Get tags as list
  List<String> getTagsList() {
    if (tags == null || tags!.isEmpty) return [];
    return tags!.split(',').map((tag) => tag.trim()).where((tag) => tag.isNotEmpty).toList();
  }

  /// Set tags from list
  void setTagsList(List<String> tagsList) {
    if (tagsList.isEmpty) {
      tags = null;
      return;
    }
    tags = tagsList.join(',');
  }

  /// Get display name
  String get displayName {
    if (name.isNotEmpty) return name;
    if (firstName != null && lastName != null) {
      return '${firstName!} ${lastName!}'.trim();
    }
    if (firstName != null) return firstName!;
    if (lastName != null) return lastName!;
    if (email != null) return email!;
    return 'Unknown Contact';
  }

  /// Get initials for avatar
  String get initials {
    final displayName = this.displayName;
    final words = displayName.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    } else if (words.isNotEmpty && words[0].isNotEmpty) {
      return words[0][0].toUpperCase();
    }
    return '?';
  }

  /// Check if contact has any contact information
  bool get hasContactInfo {
    return (email != null && email!.isNotEmpty) ||
           (phone != null && phone!.isNotEmpty);
  }

  /// Mark as needing sync
  void markForSync() {
    needsSync = true;
    syncStatus = 'pending';
    updatedAt = DateTime.now();
  }

  /// Mark as synced
  void markAsSynced(String backendId) {
    if (backendId.isNotEmpty) {
      contactId = backendId;
    }
    needsSync = false;
    syncStatus = 'synced';
  }

  /// Mark sync as failed
  void markSyncFailed() {
    syncStatus = 'error';
  }
}
