import 'package:isar/isar.dart';

part 'chat_models.g.dart';

/// Chat message model for Isar database
@collection
class ChatMessage {
  Id id = Isar.autoIncrement;

  @Index()
  late String messageId; // Backend ID

  @Index()
  late String conversationId;

  late String content;
  String? imageUrl;
  
  /// Message type: text, image, file, etc.
  late String messageType;

  /// Whether message is from user or AI
  late bool isFromUser;

  /// Message status: sending, sent, failed, received
  late String status;

  /// Message timestamp
  late DateTime timestamp;

  /// Needs sync with backend
  bool needsSync = true;

  ChatMessage();

  /// Create from API response
  factory ChatMessage.fromApi(Map<String, dynamic> json) {
    final message = ChatMessage()
      ..messageId = json['id']?.toString() ?? ''
      ..conversationId = json['conversation_id']?.toString() ?? ''
      ..content = json['content']?.toString() ?? ''
      ..imageUrl = json['image_url']?.toString()
      ..messageType = json['type']?.toString() ?? 'text'
      ..isFromUser = json['is_from_user'] == true
      ..status = json['status']?.toString() ?? 'received'
      ..needsSync = false;

    // Parse timestamp
    if (json['timestamp'] != null) {
      message.timestamp = DateTime.parse(json['timestamp']);
    } else {
      message.timestamp = DateTime.now();
    }

    return message;
  }

  /// Convert to API format
  Map<String, dynamic> toApi() {
    return {
      'id': messageId.isEmpty ? null : messageId,
      'conversation_id': conversationId,
      'content': content,
      'image_url': imageUrl,
      'type': messageType,
      'is_from_user': isFromUser,
      'status': status,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Check if message has image
  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;

  /// Check if message is pending
  bool get isPending => status == 'sending';

  /// Check if message failed
  bool get isFailed => status == 'failed';

  /// Get display time
  String get displayTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

/// Chat conversation model for Isar database
@collection
class ChatConversation {
  Id id = Isar.autoIncrement;

  @Index()
  late String conversationId; // Backend ID

  late String title;
  String? description;

  /// Last message content preview
  String? lastMessage;

  /// Last message timestamp
  late DateTime lastMessageAt;

  /// Number of messages in conversation
  int messageCount = 0;

  /// Number of unread messages
  int unreadCount = 0;

  /// Conversation creation timestamp
  late DateTime createdAt;

  /// Needs sync with backend
  bool needsSync = true;

  /// Conversation is archived
  bool isArchived = false;

  /// Conversation is pinned
  bool isPinned = false;

  ChatConversation();

  /// Create from API response
  factory ChatConversation.fromApi(Map<String, dynamic> json) {
    final conversation = ChatConversation()
      ..conversationId = json['id']?.toString() ?? ''
      ..title = json['title']?.toString() ?? ''
      ..description = json['description']?.toString()
      ..lastMessage = json['last_message']?.toString()
      ..messageCount = json['message_count']?.toInt() ?? 0
      ..unreadCount = json['unread_count']?.toInt() ?? 0
      ..isArchived = json['is_archived'] == true
      ..isPinned = json['is_pinned'] == true
      ..needsSync = false;

    // Parse timestamps
    if (json['last_message_at'] != null) {
      conversation.lastMessageAt = DateTime.parse(json['last_message_at']);
    } else {
      conversation.lastMessageAt = DateTime.now();
    }

    if (json['created_at'] != null) {
      conversation.createdAt = DateTime.parse(json['created_at']);
    } else {
      conversation.createdAt = DateTime.now();
    }

    return conversation;
  }

  /// Convert to API format
  Map<String, dynamic> toApi() {
    return {
      'id': conversationId.isEmpty ? null : conversationId,
      'title': title,
      'description': description,
      'last_message': lastMessage,
      'last_message_at': lastMessageAt.toIso8601String(),
      'message_count': messageCount,
      'unread_count': unreadCount,
      'is_archived': isArchived,
      'is_pinned': isPinned,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Get display time for last message
  String get lastMessageDisplayTime {
    final now = DateTime.now();
    final difference = now.difference(lastMessageAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }

  /// Get preview of last message
  String get lastMessagePreview {
    if (lastMessage == null || lastMessage!.isEmpty) {
      return 'No messages yet';
    }
    
    if (lastMessage!.length > 50) {
      return '${lastMessage!.substring(0, 50)}...';
    }
    
    return lastMessage!;
  }

  /// Check if conversation has unread messages
  bool get hasUnreadMessages => unreadCount > 0;

  /// Mark as read
  void markAsRead() {
    unreadCount = 0;
    needsSync = true;
  }

  /// Update last message
  void updateLastMessage(String message) {
    lastMessage = message;
    lastMessageAt = DateTime.now();
    messageCount++;
    needsSync = true;
  }
}
