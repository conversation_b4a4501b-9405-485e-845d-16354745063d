import 'dart:async';
import 'dart:io';
import 'package:flutter/services.dart';

/// Service for handling Android share intents and shared content via MethodChannel
class ShareIntentService {
  static const MethodChannel _channel = MethodChannel('com.drix.app/share');

  // Stream controllers for different types of shared content
  final StreamController<String> _textStreamController =
      StreamController<String>.broadcast();
  final StreamController<List<String>> _imageStreamController =
      StreamController<List<String>>.broadcast();

  bool _isInitialized = false;

  /// Initialize the share intent service
  Future<void> initialize() async {
    if (_isInitialized) return;
    try {
      _channel.setMethodCallHandler(_handleMethodCall);
      await _checkInitialSharedData();
      _isInitialized = true;
      // ignore: avoid_print
      print('ShareIntentService initialized');
    } catch (e) {
      // ignore: avoid_print
      print('Error initializing ShareIntentService: $e');
    }
  }

  /// Stream of shared text content
  Stream<String> get sharedTextStream => _textStreamController.stream;

  /// Stream of shared image paths (as String URIs or file paths)
  Stream<List<String>> get sharedImageStream => _imageStreamController.stream;

  /// Handle method calls from native Android code
  Future<void> _handleMethodCall(MethodCall call) async {
    if (call.method != 'handleSharedContent') {
      // ignore: avoid_print
      print('Unknown method from native: ${call.method}');
      return;
    }
    try {
      final args = Map<String, dynamic>.from(call.arguments as Map);
      await _dispatchSharedPayload(args);
    } catch (e) {
      // ignore: avoid_print
      print('Error handling shared content: $e');
    }
  }

  /// Check for initial shared data when app starts (cold start)
  Future<void> _checkInitialSharedData() async {
    try {
      final result = await _channel.invokeMethod<Map<dynamic, dynamic>?>('getInitialSharedData');
      if (result == null) return;
      final args = Map<String, dynamic>.from(result);
      await _dispatchSharedPayload(args);
    } catch (e) {
      // ignore: avoid_print
      print('Error fetching initial shared data: $e');
    }
  }

  Future<void> _dispatchSharedPayload(Map<String, dynamic> args) async {
    final type = (args['type'] as String?)?.toLowerCase();
    switch (type) {
      case 'text':
        final text = (args['content'] as String?)?.trim();
        if (text != null && text.isNotEmpty) {
          _textStreamController.add(text);
        }
        break;
      case 'image':
        final filePath = (args['filePath'] as String?)?.trim();
        final uri = (args['uri'] as String?)?.trim();
        if (filePath != null && filePath.isNotEmpty) {
          _imageStreamController.add([filePath]);
        } else if (uri != null && uri.isNotEmpty) {
          final copied = await copySharedUriToCache(uri);
          if (copied != null) {
            _imageStreamController.add([copied]);
          }
        }
        break;
      case 'images':
        final filePaths = (args['filePaths'] as List?)?.whereType<String>().map((e) => e.trim()).where((e) => e.isNotEmpty).toList() ?? const [];
        final uris = (args['uris'] as List?)?.whereType<String>().map((e) => e.trim()).where((e) => e.isNotEmpty).toList() ?? const [];
        if (filePaths.isNotEmpty) {
          _imageStreamController.add(filePaths);
        } else if (uris.isNotEmpty) {
          final copied = <String>[];
          for (final u in uris) {
            final p = await copySharedUriToCache(u);
            if (p != null) copied.add(p);
          }
          if (copied.isNotEmpty) {
            _imageStreamController.add(copied);
          }
        }
        break;
      default:
        // ignore: avoid_print
        print('Unsupported shared type: $type');
    }
  }

  /// Utility: Check if a string is likely a URL
  bool isUrl(String text) {
    final t = text.trim();
    if (t.isEmpty) return false;
    final urlRegex = RegExp(r'^(https?:\/\/)?([\w-]+\.)+[\w-]{2,}(\/[^\s]*)?$', caseSensitive: false);
    return urlRegex.hasMatch(t);
  }

  /// Utility: Extract URLs from text
  List<String> extractUrls(String text) {
    final regex = RegExp(r'(https?:\/\/[\w\-]+(\.[\w\-]+)+(:\d+)?(\/[\w\-.,@?^=%&:/~+#]*)?)', caseSensitive: false);
    return regex.allMatches(text).map((m) => m.group(0)!).toList();
  }

  /// Utility: Basic cleanup of shared text (trim, normalize whitespace)
  String cleanSharedText(String text) {
    return text.replaceAll('\r', ' ').replaceAll('\n', ' ').replaceAll(RegExp(r'\s+'), ' ').trim();
  }

  /// Utility: Validate that shared content is meaningful (non-empty, not only whitespace)
  bool isValidSharedContent(String text) {
    return cleanSharedText(text).isNotEmpty && cleanSharedText(text).length > 2;
  }

  /// Helper used by tests to differentiate content types
  String getContentType(String text) {
    final clean = cleanSharedText(text);
    final urls = extractUrls(clean);
    if (urls.isNotEmpty && urls.first == clean) return 'url';
    if (urls.isNotEmpty) return 'text_with_urls';
    return 'text';
  }

  /// Ask native layer to copy a content:// URI into a temporary cache file and return absolute path
  Future<String?> copySharedUriToCache(String uri) async {
    try {
      final path = await _channel.invokeMethod<String>('copySharedUriToCache', {
        'uri': uri,
      });
      return path;
    } catch (e) {
      // ignore: avoid_print
      print('Error copying shared URI to cache: $e');
      return null;
    }
  }

  /// Dispose resources
  void dispose() {
    _textStreamController.close();
    _imageStreamController.close();
  }
}
