// Mocks generated by <PERSON><PERSON><PERSON> 5.4.4 from annotations
// in darvis_app/test/firebase_auth_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;
import 'dart:io' as _i10;

import 'package:darvis_app/services/api_service.dart' as _i9;
import 'package:firebase_auth/firebase_auth.dart' as _i4;
import 'package:firebase_auth_platform_interface/firebase_auth_platform_interface.dart'
    as _i3;
import 'package:firebase_core/firebase_core.dart' as _i2;
import 'package:flutter/foundation.dart' as _i11;
import 'package:flutter_secure_storage/flutter_secure_storage.dart' as _i5;
import 'package:google_sign_in/google_sign_in.dart' as _i6;
import 'package:google_sign_in_platform_interface/google_sign_in_platform_interface.dart'
    as _i12;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFirebaseApp_0 extends _i1.SmartFake implements _i2.FirebaseApp {
  _FakeFirebaseApp_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeActionCodeInfo_1 extends _i1.SmartFake
    implements _i3.ActionCodeInfo {
  _FakeActionCodeInfo_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUserCredential_2 extends _i1.SmartFake
    implements _i4.UserCredential {
  _FakeUserCredential_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeConfirmationResult_3 extends _i1.SmartFake
    implements _i4.ConfirmationResult {
  _FakeConfirmationResult_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUserMetadata_4 extends _i1.SmartFake implements _i3.UserMetadata {
  _FakeUserMetadata_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMultiFactor_5 extends _i1.SmartFake implements _i4.MultiFactor {
  _FakeMultiFactor_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeIdTokenResult_6 extends _i1.SmartFake implements _i3.IdTokenResult {
  _FakeIdTokenResult_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUser_7 extends _i1.SmartFake implements _i4.User {
  _FakeUser_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeIOSOptions_8 extends _i1.SmartFake implements _i5.IOSOptions {
  _FakeIOSOptions_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeAndroidOptions_9 extends _i1.SmartFake
    implements _i5.AndroidOptions {
  _FakeAndroidOptions_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeLinuxOptions_10 extends _i1.SmartFake implements _i5.LinuxOptions {
  _FakeLinuxOptions_10(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeWindowsOptions_11 extends _i1.SmartFake
    implements _i5.WindowsOptions {
  _FakeWindowsOptions_11(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeWebOptions_12 extends _i1.SmartFake implements _i5.WebOptions {
  _FakeWebOptions_12(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMacOsOptions_13 extends _i1.SmartFake implements _i5.MacOsOptions {
  _FakeMacOsOptions_13(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGoogleSignInAuthentication_14 extends _i1.SmartFake
    implements _i6.GoogleSignInAuthentication {
  _FakeGoogleSignInAuthentication_14(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [FirebaseAuth].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseAuth extends _i1.Mock implements _i4.FirebaseAuth {
  MockFirebaseAuth() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FirebaseApp get app => (super.noSuchMethod(
        Invocation.getter(#app),
        returnValue: _FakeFirebaseApp_0(
          this,
          Invocation.getter(#app),
        ),
      ) as _i2.FirebaseApp);

  @override
  set app(_i2.FirebaseApp? _app) => super.noSuchMethod(
        Invocation.setter(
          #app,
          _app,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set tenantId(String? tenantId) => super.noSuchMethod(
        Invocation.setter(
          #tenantId,
          tenantId,
        ),
        returnValueForMissingStub: null,
      );

  @override
  Map<dynamic, dynamic> get pluginConstants => (super.noSuchMethod(
        Invocation.getter(#pluginConstants),
        returnValue: <dynamic, dynamic>{},
      ) as Map<dynamic, dynamic>);

  @override
  _i7.Future<void> useEmulator(String? origin) => (super.noSuchMethod(
        Invocation.method(
          #useEmulator,
          [origin],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> useAuthEmulator(
    String? host,
    int? port, {
    bool? automaticHostMapping = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #useAuthEmulator,
          [
            host,
            port,
          ],
          {#automaticHostMapping: automaticHostMapping},
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> applyActionCode(String? code) => (super.noSuchMethod(
        Invocation.method(
          #applyActionCode,
          [code],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<_i3.ActionCodeInfo> checkActionCode(String? code) =>
      (super.noSuchMethod(
        Invocation.method(
          #checkActionCode,
          [code],
        ),
        returnValue: _i7.Future<_i3.ActionCodeInfo>.value(_FakeActionCodeInfo_1(
          this,
          Invocation.method(
            #checkActionCode,
            [code],
          ),
        )),
      ) as _i7.Future<_i3.ActionCodeInfo>);

  @override
  _i7.Future<void> confirmPasswordReset({
    required String? code,
    required String? newPassword,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #confirmPasswordReset,
          [],
          {
            #code: code,
            #newPassword: newPassword,
          },
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<_i4.UserCredential> createUserWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createUserWithEmailAndPassword,
          [],
          {
            #email: email,
            #password: password,
          },
        ),
        returnValue: _i7.Future<_i4.UserCredential>.value(_FakeUserCredential_2(
          this,
          Invocation.method(
            #createUserWithEmailAndPassword,
            [],
            {
              #email: email,
              #password: password,
            },
          ),
        )),
      ) as _i7.Future<_i4.UserCredential>);

  @override
  _i7.Future<List<String>> fetchSignInMethodsForEmail(String? email) =>
      (super.noSuchMethod(
        Invocation.method(
          #fetchSignInMethodsForEmail,
          [email],
        ),
        returnValue: _i7.Future<List<String>>.value(<String>[]),
      ) as _i7.Future<List<String>>);

  @override
  _i7.Future<_i4.UserCredential> getRedirectResult() => (super.noSuchMethod(
        Invocation.method(
          #getRedirectResult,
          [],
        ),
        returnValue: _i7.Future<_i4.UserCredential>.value(_FakeUserCredential_2(
          this,
          Invocation.method(
            #getRedirectResult,
            [],
          ),
        )),
      ) as _i7.Future<_i4.UserCredential>);

  @override
  bool isSignInWithEmailLink(String? emailLink) => (super.noSuchMethod(
        Invocation.method(
          #isSignInWithEmailLink,
          [emailLink],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i7.Stream<_i4.User?> authStateChanges() => (super.noSuchMethod(
        Invocation.method(
          #authStateChanges,
          [],
        ),
        returnValue: _i7.Stream<_i4.User?>.empty(),
      ) as _i7.Stream<_i4.User?>);

  @override
  _i7.Stream<_i4.User?> idTokenChanges() => (super.noSuchMethod(
        Invocation.method(
          #idTokenChanges,
          [],
        ),
        returnValue: _i7.Stream<_i4.User?>.empty(),
      ) as _i7.Stream<_i4.User?>);

  @override
  _i7.Stream<_i4.User?> userChanges() => (super.noSuchMethod(
        Invocation.method(
          #userChanges,
          [],
        ),
        returnValue: _i7.Stream<_i4.User?>.empty(),
      ) as _i7.Stream<_i4.User?>);

  @override
  _i7.Future<void> sendPasswordResetEmail({
    required String? email,
    _i3.ActionCodeSettings? actionCodeSettings,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendPasswordResetEmail,
          [],
          {
            #email: email,
            #actionCodeSettings: actionCodeSettings,
          },
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> sendSignInLinkToEmail({
    required String? email,
    required _i3.ActionCodeSettings? actionCodeSettings,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendSignInLinkToEmail,
          [],
          {
            #email: email,
            #actionCodeSettings: actionCodeSettings,
          },
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> setLanguageCode(String? languageCode) => (super.noSuchMethod(
        Invocation.method(
          #setLanguageCode,
          [languageCode],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> setSettings({
    bool? appVerificationDisabledForTesting = false,
    String? userAccessGroup,
    String? phoneNumber,
    String? smsCode,
    bool? forceRecaptchaFlow,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #setSettings,
          [],
          {
            #appVerificationDisabledForTesting:
                appVerificationDisabledForTesting,
            #userAccessGroup: userAccessGroup,
            #phoneNumber: phoneNumber,
            #smsCode: smsCode,
            #forceRecaptchaFlow: forceRecaptchaFlow,
          },
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> setPersistence(_i3.Persistence? persistence) =>
      (super.noSuchMethod(
        Invocation.method(
          #setPersistence,
          [persistence],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<_i4.UserCredential> signInAnonymously() => (super.noSuchMethod(
        Invocation.method(
          #signInAnonymously,
          [],
        ),
        returnValue: _i7.Future<_i4.UserCredential>.value(_FakeUserCredential_2(
          this,
          Invocation.method(
            #signInAnonymously,
            [],
          ),
        )),
      ) as _i7.Future<_i4.UserCredential>);

  @override
  _i7.Future<_i4.UserCredential> signInWithCredential(
          _i3.AuthCredential? credential) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithCredential,
          [credential],
        ),
        returnValue: _i7.Future<_i4.UserCredential>.value(_FakeUserCredential_2(
          this,
          Invocation.method(
            #signInWithCredential,
            [credential],
          ),
        )),
      ) as _i7.Future<_i4.UserCredential>);

  @override
  _i7.Future<_i4.UserCredential> signInWithCustomToken(String? token) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithCustomToken,
          [token],
        ),
        returnValue: _i7.Future<_i4.UserCredential>.value(_FakeUserCredential_2(
          this,
          Invocation.method(
            #signInWithCustomToken,
            [token],
          ),
        )),
      ) as _i7.Future<_i4.UserCredential>);

  @override
  _i7.Future<_i4.UserCredential> signInWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithEmailAndPassword,
          [],
          {
            #email: email,
            #password: password,
          },
        ),
        returnValue: _i7.Future<_i4.UserCredential>.value(_FakeUserCredential_2(
          this,
          Invocation.method(
            #signInWithEmailAndPassword,
            [],
            {
              #email: email,
              #password: password,
            },
          ),
        )),
      ) as _i7.Future<_i4.UserCredential>);

  @override
  _i7.Future<_i4.UserCredential> signInWithEmailLink({
    required String? email,
    required String? emailLink,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithEmailLink,
          [],
          {
            #email: email,
            #emailLink: emailLink,
          },
        ),
        returnValue: _i7.Future<_i4.UserCredential>.value(_FakeUserCredential_2(
          this,
          Invocation.method(
            #signInWithEmailLink,
            [],
            {
              #email: email,
              #emailLink: emailLink,
            },
          ),
        )),
      ) as _i7.Future<_i4.UserCredential>);

  @override
  _i7.Future<_i4.UserCredential> signInWithAuthProvider(
          _i3.AuthProvider? provider) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithAuthProvider,
          [provider],
        ),
        returnValue: _i7.Future<_i4.UserCredential>.value(_FakeUserCredential_2(
          this,
          Invocation.method(
            #signInWithAuthProvider,
            [provider],
          ),
        )),
      ) as _i7.Future<_i4.UserCredential>);

  @override
  _i7.Future<_i4.UserCredential> signInWithProvider(
          _i3.AuthProvider? provider) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithProvider,
          [provider],
        ),
        returnValue: _i7.Future<_i4.UserCredential>.value(_FakeUserCredential_2(
          this,
          Invocation.method(
            #signInWithProvider,
            [provider],
          ),
        )),
      ) as _i7.Future<_i4.UserCredential>);

  @override
  _i7.Future<_i4.ConfirmationResult> signInWithPhoneNumber(
    String? phoneNumber, [
    _i4.RecaptchaVerifier? verifier,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithPhoneNumber,
          [
            phoneNumber,
            verifier,
          ],
        ),
        returnValue:
            _i7.Future<_i4.ConfirmationResult>.value(_FakeConfirmationResult_3(
          this,
          Invocation.method(
            #signInWithPhoneNumber,
            [
              phoneNumber,
              verifier,
            ],
          ),
        )),
      ) as _i7.Future<_i4.ConfirmationResult>);

  @override
  _i7.Future<_i4.UserCredential> signInWithPopup(_i3.AuthProvider? provider) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithPopup,
          [provider],
        ),
        returnValue: _i7.Future<_i4.UserCredential>.value(_FakeUserCredential_2(
          this,
          Invocation.method(
            #signInWithPopup,
            [provider],
          ),
        )),
      ) as _i7.Future<_i4.UserCredential>);

  @override
  _i7.Future<void> signInWithRedirect(_i3.AuthProvider? provider) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithRedirect,
          [provider],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> signOut() => (super.noSuchMethod(
        Invocation.method(
          #signOut,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<String> verifyPasswordResetCode(String? code) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyPasswordResetCode,
          [code],
        ),
        returnValue: _i7.Future<String>.value(_i8.dummyValue<String>(
          this,
          Invocation.method(
            #verifyPasswordResetCode,
            [code],
          ),
        )),
      ) as _i7.Future<String>);

  @override
  _i7.Future<void> verifyPhoneNumber({
    String? phoneNumber,
    _i3.PhoneMultiFactorInfo? multiFactorInfo,
    required _i3.PhoneVerificationCompleted? verificationCompleted,
    required _i3.PhoneVerificationFailed? verificationFailed,
    required _i3.PhoneCodeSent? codeSent,
    required _i3.PhoneCodeAutoRetrievalTimeout? codeAutoRetrievalTimeout,
    String? autoRetrievedSmsCodeForTesting,
    Duration? timeout = const Duration(seconds: 30),
    int? forceResendingToken,
    _i3.MultiFactorSession? multiFactorSession,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyPhoneNumber,
          [],
          {
            #phoneNumber: phoneNumber,
            #multiFactorInfo: multiFactorInfo,
            #verificationCompleted: verificationCompleted,
            #verificationFailed: verificationFailed,
            #codeSent: codeSent,
            #codeAutoRetrievalTimeout: codeAutoRetrievalTimeout,
            #autoRetrievedSmsCodeForTesting: autoRetrievedSmsCodeForTesting,
            #timeout: timeout,
            #forceResendingToken: forceResendingToken,
            #multiFactorSession: multiFactorSession,
          },
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> revokeTokenWithAuthorizationCode(
          String? authorizationCode) =>
      (super.noSuchMethod(
        Invocation.method(
          #revokeTokenWithAuthorizationCode,
          [authorizationCode],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);
}

/// A class which mocks [User].
///
/// See the documentation for Mockito's code generation for more information.
class MockUser extends _i1.Mock implements _i4.User {
  MockUser() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get emailVerified => (super.noSuchMethod(
        Invocation.getter(#emailVerified),
        returnValue: false,
      ) as bool);

  @override
  bool get isAnonymous => (super.noSuchMethod(
        Invocation.getter(#isAnonymous),
        returnValue: false,
      ) as bool);

  @override
  _i3.UserMetadata get metadata => (super.noSuchMethod(
        Invocation.getter(#metadata),
        returnValue: _FakeUserMetadata_4(
          this,
          Invocation.getter(#metadata),
        ),
      ) as _i3.UserMetadata);

  @override
  List<_i3.UserInfo> get providerData => (super.noSuchMethod(
        Invocation.getter(#providerData),
        returnValue: <_i3.UserInfo>[],
      ) as List<_i3.UserInfo>);

  @override
  String get uid => (super.noSuchMethod(
        Invocation.getter(#uid),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#uid),
        ),
      ) as String);

  @override
  _i4.MultiFactor get multiFactor => (super.noSuchMethod(
        Invocation.getter(#multiFactor),
        returnValue: _FakeMultiFactor_5(
          this,
          Invocation.getter(#multiFactor),
        ),
      ) as _i4.MultiFactor);

  @override
  _i7.Future<void> delete() => (super.noSuchMethod(
        Invocation.method(
          #delete,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<String?> getIdToken([bool? forceRefresh = false]) =>
      (super.noSuchMethod(
        Invocation.method(
          #getIdToken,
          [forceRefresh],
        ),
        returnValue: _i7.Future<String?>.value(),
      ) as _i7.Future<String?>);

  @override
  _i7.Future<_i3.IdTokenResult> getIdTokenResult(
          [bool? forceRefresh = false]) =>
      (super.noSuchMethod(
        Invocation.method(
          #getIdTokenResult,
          [forceRefresh],
        ),
        returnValue: _i7.Future<_i3.IdTokenResult>.value(_FakeIdTokenResult_6(
          this,
          Invocation.method(
            #getIdTokenResult,
            [forceRefresh],
          ),
        )),
      ) as _i7.Future<_i3.IdTokenResult>);

  @override
  _i7.Future<_i4.UserCredential> linkWithCredential(
          _i3.AuthCredential? credential) =>
      (super.noSuchMethod(
        Invocation.method(
          #linkWithCredential,
          [credential],
        ),
        returnValue: _i7.Future<_i4.UserCredential>.value(_FakeUserCredential_2(
          this,
          Invocation.method(
            #linkWithCredential,
            [credential],
          ),
        )),
      ) as _i7.Future<_i4.UserCredential>);

  @override
  _i7.Future<_i4.UserCredential> linkWithProvider(_i3.AuthProvider? provider) =>
      (super.noSuchMethod(
        Invocation.method(
          #linkWithProvider,
          [provider],
        ),
        returnValue: _i7.Future<_i4.UserCredential>.value(_FakeUserCredential_2(
          this,
          Invocation.method(
            #linkWithProvider,
            [provider],
          ),
        )),
      ) as _i7.Future<_i4.UserCredential>);

  @override
  _i7.Future<_i4.UserCredential> reauthenticateWithProvider(
          _i3.AuthProvider? provider) =>
      (super.noSuchMethod(
        Invocation.method(
          #reauthenticateWithProvider,
          [provider],
        ),
        returnValue: _i7.Future<_i4.UserCredential>.value(_FakeUserCredential_2(
          this,
          Invocation.method(
            #reauthenticateWithProvider,
            [provider],
          ),
        )),
      ) as _i7.Future<_i4.UserCredential>);

  @override
  _i7.Future<_i4.UserCredential> reauthenticateWithPopup(
          _i3.AuthProvider? provider) =>
      (super.noSuchMethod(
        Invocation.method(
          #reauthenticateWithPopup,
          [provider],
        ),
        returnValue: _i7.Future<_i4.UserCredential>.value(_FakeUserCredential_2(
          this,
          Invocation.method(
            #reauthenticateWithPopup,
            [provider],
          ),
        )),
      ) as _i7.Future<_i4.UserCredential>);

  @override
  _i7.Future<void> reauthenticateWithRedirect(_i3.AuthProvider? provider) =>
      (super.noSuchMethod(
        Invocation.method(
          #reauthenticateWithRedirect,
          [provider],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<_i4.UserCredential> linkWithPopup(_i3.AuthProvider? provider) =>
      (super.noSuchMethod(
        Invocation.method(
          #linkWithPopup,
          [provider],
        ),
        returnValue: _i7.Future<_i4.UserCredential>.value(_FakeUserCredential_2(
          this,
          Invocation.method(
            #linkWithPopup,
            [provider],
          ),
        )),
      ) as _i7.Future<_i4.UserCredential>);

  @override
  _i7.Future<void> linkWithRedirect(_i3.AuthProvider? provider) =>
      (super.noSuchMethod(
        Invocation.method(
          #linkWithRedirect,
          [provider],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<_i4.ConfirmationResult> linkWithPhoneNumber(
    String? phoneNumber, [
    _i4.RecaptchaVerifier? verifier,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #linkWithPhoneNumber,
          [
            phoneNumber,
            verifier,
          ],
        ),
        returnValue:
            _i7.Future<_i4.ConfirmationResult>.value(_FakeConfirmationResult_3(
          this,
          Invocation.method(
            #linkWithPhoneNumber,
            [
              phoneNumber,
              verifier,
            ],
          ),
        )),
      ) as _i7.Future<_i4.ConfirmationResult>);

  @override
  _i7.Future<_i4.UserCredential> reauthenticateWithCredential(
          _i3.AuthCredential? credential) =>
      (super.noSuchMethod(
        Invocation.method(
          #reauthenticateWithCredential,
          [credential],
        ),
        returnValue: _i7.Future<_i4.UserCredential>.value(_FakeUserCredential_2(
          this,
          Invocation.method(
            #reauthenticateWithCredential,
            [credential],
          ),
        )),
      ) as _i7.Future<_i4.UserCredential>);

  @override
  _i7.Future<void> reload() => (super.noSuchMethod(
        Invocation.method(
          #reload,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> sendEmailVerification(
          [_i3.ActionCodeSettings? actionCodeSettings]) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendEmailVerification,
          [actionCodeSettings],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<_i4.User> unlink(String? providerId) => (super.noSuchMethod(
        Invocation.method(
          #unlink,
          [providerId],
        ),
        returnValue: _i7.Future<_i4.User>.value(_FakeUser_7(
          this,
          Invocation.method(
            #unlink,
            [providerId],
          ),
        )),
      ) as _i7.Future<_i4.User>);

  @override
  _i7.Future<void> updateEmail(String? newEmail) => (super.noSuchMethod(
        Invocation.method(
          #updateEmail,
          [newEmail],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> updatePassword(String? newPassword) => (super.noSuchMethod(
        Invocation.method(
          #updatePassword,
          [newPassword],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> updatePhoneNumber(
          _i3.PhoneAuthCredential? phoneCredential) =>
      (super.noSuchMethod(
        Invocation.method(
          #updatePhoneNumber,
          [phoneCredential],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> updateDisplayName(String? displayName) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateDisplayName,
          [displayName],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> updatePhotoURL(String? photoURL) => (super.noSuchMethod(
        Invocation.method(
          #updatePhotoURL,
          [photoURL],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> updateProfile({
    String? displayName,
    String? photoURL,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateProfile,
          [],
          {
            #displayName: displayName,
            #photoURL: photoURL,
          },
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<void> verifyBeforeUpdateEmail(
    String? newEmail, [
    _i3.ActionCodeSettings? actionCodeSettings,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyBeforeUpdateEmail,
          [
            newEmail,
            actionCodeSettings,
          ],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);
}

/// A class which mocks [UserCredential].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserCredential extends _i1.Mock implements _i4.UserCredential {
  MockUserCredential() {
    _i1.throwOnMissingStub(this);
  }
}

/// A class which mocks [ApiService].
///
/// See the documentation for Mockito's code generation for more information.
class MockApiService extends _i1.Mock implements _i9.ApiService {
  MockApiService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Future<Map<String, dynamic>> login({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #login,
          [],
          {
            #email: email,
            #password: password,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> register({
    required String? email,
    required String? password,
    required String? name,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #register,
          [],
          {
            #email: email,
            #password: password,
            #name: name,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> refreshToken(String? refreshToken) =>
      (super.noSuchMethod(
        Invocation.method(
          #refreshToken,
          [refreshToken],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> exchangeFirebaseToken(String? idToken) =>
      (super.noSuchMethod(
        Invocation.method(
          #exchangeFirebaseToken,
          [idToken],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> syncUserProfile(
          Map<String, dynamic>? profileData) =>
      (super.noSuchMethod(
        Invocation.method(
          #syncUserProfile,
          [profileData],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> updateUserSession() => (super.noSuchMethod(
        Invocation.method(
          #updateUserSession,
          [],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> getUserSessionData() => (super.noSuchMethod(
        Invocation.method(
          #getUserSessionData,
          [],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> createNotification(
          dynamic notificationDto) =>
      (super.noSuchMethod(
        Invocation.method(
          #createNotification,
          [notificationDto],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> updateNotificationSettings(
          dynamic settingsDto) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateNotificationSettings,
          [settingsDto],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> markNotificationAsInteracted(
          String? notificationId) =>
      (super.noSuchMethod(
        Invocation.method(
          #markNotificationAsInteracted,
          [notificationId],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> getUserNotifications({
    int? page = 1,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserNotifications,
          [],
          {
            #page: page,
            #limit: limit,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> updateUserProfile(dynamic profileDto) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateUserProfile,
          [profileDto],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> updateProfilePicture(
          dynamic updateRequest) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateProfilePicture,
          [updateRequest],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> getUserProfile() => (super.noSuchMethod(
        Invocation.method(
          #getUserProfile,
          [],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> deleteProfilePicture() =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteProfilePicture,
          [],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> getDashboardData() => (super.noSuchMethod(
        Invocation.method(
          #getDashboardData,
          [],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> sendMessage({
    required String? message,
    required String? conversationId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendMessage,
          [],
          {
            #message: message,
            #conversationId: conversationId,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<List<Map<String, dynamic>>> getConversations() =>
      (super.noSuchMethod(
        Invocation.method(
          #getConversations,
          [],
        ),
        returnValue: _i7.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
      ) as _i7.Future<List<Map<String, dynamic>>>);

  @override
  _i7.Future<Map<String, dynamic>> createConversation({
    required String? title,
    String? description,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createConversation,
          [],
          {
            #title: title,
            #description: description,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<void> deleteConversation(String? conversationId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteConversation,
          [conversationId],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<Map<String, dynamic>> createTask({
    required String? title,
    required String? description,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createTask,
          [],
          {
            #title: title,
            #description: description,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<List<Map<String, dynamic>>> getTasks() => (super.noSuchMethod(
        Invocation.method(
          #getTasks,
          [],
        ),
        returnValue: _i7.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
      ) as _i7.Future<List<Map<String, dynamic>>>);

  @override
  _i7.Future<Map<String, dynamic>> updateTask({
    required String? taskId,
    Map<String, dynamic>? updates,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateTask,
          [],
          {
            #taskId: taskId,
            #updates: updates,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> createNote({
    required String? title,
    required String? content,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createNote,
          [],
          {
            #title: title,
            #content: content,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<List<Map<String, dynamic>>> getNotes() => (super.noSuchMethod(
        Invocation.method(
          #getNotes,
          [],
        ),
        returnValue: _i7.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
      ) as _i7.Future<List<Map<String, dynamic>>>);

  @override
  _i7.Future<Map<String, dynamic>> updateNote({
    required String? noteId,
    Map<String, dynamic>? updates,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateNote,
          [],
          {
            #noteId: noteId,
            #updates: updates,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> deleteNote(String? noteId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteNote,
          [noteId],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> deleteTask(String? taskId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteTask,
          [taskId],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> createCalendarEvent({
    required String? title,
    required String? description,
    required DateTime? startTime,
    required DateTime? endTime,
    String? type,
    bool? isAllDay,
    String? location,
    List<String>? attendees,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createCalendarEvent,
          [],
          {
            #title: title,
            #description: description,
            #startTime: startTime,
            #endTime: endTime,
            #type: type,
            #isAllDay: isAllDay,
            #location: location,
            #attendees: attendees,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<List<Map<String, dynamic>>> getCalendarEvents({
    DateTime? startDate,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCalendarEvents,
          [],
          {
            #startDate: startDate,
            #endDate: endDate,
          },
        ),
        returnValue: _i7.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
      ) as _i7.Future<List<Map<String, dynamic>>>);

  @override
  _i7.Future<Map<String, dynamic>> updateCalendarEvent({
    required String? eventId,
    Map<String, dynamic>? updates,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateCalendarEvent,
          [],
          {
            #eventId: eventId,
            #updates: updates,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> deleteCalendarEvent(String? eventId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteCalendarEvent,
          [eventId],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> searchNotes({
    required String? query,
    String? tag,
    int? page = 1,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchNotes,
          [],
          {
            #query: query,
            #tag: tag,
            #page: page,
            #limit: limit,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> searchTasks({
    required String? query,
    String? status,
    String? priority,
    int? page = 1,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchTasks,
          [],
          {
            #query: query,
            #status: status,
            #priority: priority,
            #page: page,
            #limit: limit,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> searchCalendarEvents({
    required String? query,
    String? type,
    DateTime? startDate,
    DateTime? endDate,
    int? page = 1,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchCalendarEvents,
          [],
          {
            #query: query,
            #type: type,
            #startDate: startDate,
            #endDate: endDate,
            #page: page,
            #limit: limit,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> createContact({
    required String? name,
    required String? phone,
    String? email,
    String? location,
    String? metAt,
    Map<String, String>? socialMedia,
    String? memoryPrompt,
    String? imagePath,
    String? imagePublicId,
    String? deviceSyncStatus = r'disabled',
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createContact,
          [],
          {
            #name: name,
            #phone: phone,
            #email: email,
            #location: location,
            #metAt: metAt,
            #socialMedia: socialMedia,
            #memoryPrompt: memoryPrompt,
            #imagePath: imagePath,
            #imagePublicId: imagePublicId,
            #deviceSyncStatus: deviceSyncStatus,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> getContacts({
    String? search,
    String? platform,
    int? limit = 50,
    int? offset = 0,
    String? sortBy = r'name',
    String? sortOrder = r'asc',
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getContacts,
          [],
          {
            #search: search,
            #platform: platform,
            #limit: limit,
            #offset: offset,
            #sortBy: sortBy,
            #sortOrder: sortOrder,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> getContact(String? contactId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getContact,
          [contactId],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> updateContact({
    required String? contactId,
    String? name,
    String? phone,
    String? email,
    String? location,
    String? metAt,
    Map<String, String>? socialMedia,
    String? memoryPrompt,
    String? imagePath,
    String? imagePublicId,
    String? deviceSyncStatus,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateContact,
          [],
          {
            #contactId: contactId,
            #name: name,
            #phone: phone,
            #email: email,
            #location: location,
            #metAt: metAt,
            #socialMedia: socialMedia,
            #memoryPrompt: memoryPrompt,
            #imagePath: imagePath,
            #imagePublicId: imagePublicId,
            #deviceSyncStatus: deviceSyncStatus,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> deleteContact(String? contactId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteContact,
          [contactId],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<String> getContactVCF(String? contactId) => (super.noSuchMethod(
        Invocation.method(
          #getContactVCF,
          [contactId],
        ),
        returnValue: _i7.Future<String>.value(_i8.dummyValue<String>(
          this,
          Invocation.method(
            #getContactVCF,
            [contactId],
          ),
        )),
      ) as _i7.Future<String>);

  @override
  _i7.Future<Map<String, dynamic>> bulkExportContacts({
    required List<String>? contactIds,
    String? format = r'vcf',
    bool? includeImages = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #bulkExportContacts,
          [],
          {
            #contactIds: contactIds,
            #format: format,
            #includeImages: includeImages,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> exportAllContacts({
    String? format = r'vcf',
    bool? includeImages = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #exportAllContacts,
          [],
          {
            #format: format,
            #includeImages: includeImages,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> updateDeviceSync({
    required String? contactId,
    String? deviceContactId,
    required String? syncStatus,
    String? syncError,
    Map<String, String>? deviceInfo,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateDeviceSync,
          [],
          {
            #contactId: contactId,
            #deviceContactId: deviceContactId,
            #syncStatus: syncStatus,
            #syncError: syncError,
            #deviceInfo: deviceInfo,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> getContactsSyncStatus() =>
      (super.noSuchMethod(
        Invocation.method(
          #getContactsSyncStatus,
          [],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> bulkDeviceSync({
    required List<String>? contactIds,
    required Map<String, String>? deviceInfo,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #bulkDeviceSync,
          [],
          {
            #contactIds: contactIds,
            #deviceInfo: deviceInfo,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> searchContacts({
    required String? query,
    String? fields,
    String? platform,
    String? dateFrom,
    String? dateTo,
    int? limit = 20,
    int? offset = 0,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchContacts,
          [],
          {
            #query: query,
            #fields: fields,
            #platform: platform,
            #dateFrom: dateFrom,
            #dateTo: dateTo,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> captureContent({
    required String? url,
    String? title,
    String? description,
    List<String>? tags,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #captureContent,
          [],
          {
            #url: url,
            #title: title,
            #description: description,
            #tags: tags,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> uploadAndProcessImage(_i10.File? image) =>
      (super.noSuchMethod(
        Invocation.method(
          #uploadAndProcessImage,
          [image],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> getCapturedContent({
    String? search,
    String? contentType,
    String? status,
    List<String>? tags,
    int? limit = 20,
    int? offset = 0,
    String? sortBy = r'created_at',
    String? sortOrder = r'desc',
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCapturedContent,
          [],
          {
            #search: search,
            #contentType: contentType,
            #status: status,
            #tags: tags,
            #limit: limit,
            #offset: offset,
            #sortBy: sortBy,
            #sortOrder: sortOrder,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> getCapturedContentById(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCapturedContentById,
          [id],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> updateCapturedContent({
    required String? id,
    String? title,
    String? summary,
    List<String>? tags,
    String? status,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateCapturedContent,
          [],
          {
            #id: id,
            #title: title,
            #summary: summary,
            #tags: tags,
            #status: status,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<void> deleteCapturedContent(String? id) => (super.noSuchMethod(
        Invocation.method(
          #deleteCapturedContent,
          [id],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<Map<String, dynamic>> bulkDeleteCapturedContent(
          List<String>? ids) =>
      (super.noSuchMethod(
        Invocation.method(
          #bulkDeleteCapturedContent,
          [ids],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> searchCapturedContent({
    required String? query,
    String? contentType,
    List<String>? tags,
    String? dateFrom,
    String? dateTo,
    int? limit = 20,
    int? offset = 0,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchCapturedContent,
          [],
          {
            #query: query,
            #contentType: contentType,
            #tags: tags,
            #dateFrom: dateFrom,
            #dateTo: dateTo,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> getProcessingStatus(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #getProcessingStatus,
          [id],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> retryContentProcessing(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #retryContentProcessing,
          [id],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> getCapturedContentTags() =>
      (super.noSuchMethod(
        Invocation.method(
          #getCapturedContentTags,
          [],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> getCapturedContentAnalytics({
    String? dateFrom,
    String? dateTo,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCapturedContentAnalytics,
          [],
          {
            #dateFrom: dateFrom,
            #dateTo: dateTo,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> importContentToNotes({
    required String? contentId,
    String? noteTitle,
    String? noteContent,
    List<String>? tags,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #importContentToNotes,
          [],
          {
            #contentId: contentId,
            #noteTitle: noteTitle,
            #noteContent: noteContent,
            #tags: tags,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> addContentToChatContext({
    required String? contentId,
    String? conversationId,
    String? contextMessage,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addContentToChatContext,
          [],
          {
            #contentId: contentId,
            #conversationId: conversationId,
            #contextMessage: contextMessage,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> createEventFromContent({
    required String? contentId,
    required String? title,
    required DateTime? startTime,
    required DateTime? endTime,
    String? description,
    String? location,
    List<String>? attendees,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createEventFromContent,
          [],
          {
            #contentId: contentId,
            #title: title,
            #startTime: startTime,
            #endTime: endTime,
            #description: description,
            #location: location,
            #attendees: attendees,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> getContactAnalytics() => (super.noSuchMethod(
        Invocation.method(
          #getContactAnalytics,
          [],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> getSocialPlatforms() => (super.noSuchMethod(
        Invocation.method(
          #getSocialPlatforms,
          [],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> getSocialPlatform(String? platform) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSocialPlatform,
          [platform],
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> validateSocialUsername({
    required String? platform,
    required String? username,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #validateSocialUsername,
          [],
          {
            #platform: platform,
            #username: username,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);

  @override
  _i7.Future<Map<String, dynamic>> verifySocialProfile({
    required String? contactId,
    required String? platform,
    required String? username,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifySocialProfile,
          [],
          {
            #contactId: contactId,
            #platform: platform,
            #username: username,
          },
        ),
        returnValue:
            _i7.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i7.Future<Map<String, dynamic>>);
}

/// A class which mocks [FlutterSecureStorage].
///
/// See the documentation for Mockito's code generation for more information.
class MockFlutterSecureStorage extends _i1.Mock
    implements _i5.FlutterSecureStorage {
  MockFlutterSecureStorage() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.IOSOptions get iOptions => (super.noSuchMethod(
        Invocation.getter(#iOptions),
        returnValue: _FakeIOSOptions_8(
          this,
          Invocation.getter(#iOptions),
        ),
      ) as _i5.IOSOptions);

  @override
  _i5.AndroidOptions get aOptions => (super.noSuchMethod(
        Invocation.getter(#aOptions),
        returnValue: _FakeAndroidOptions_9(
          this,
          Invocation.getter(#aOptions),
        ),
      ) as _i5.AndroidOptions);

  @override
  _i5.LinuxOptions get lOptions => (super.noSuchMethod(
        Invocation.getter(#lOptions),
        returnValue: _FakeLinuxOptions_10(
          this,
          Invocation.getter(#lOptions),
        ),
      ) as _i5.LinuxOptions);

  @override
  _i5.WindowsOptions get wOptions => (super.noSuchMethod(
        Invocation.getter(#wOptions),
        returnValue: _FakeWindowsOptions_11(
          this,
          Invocation.getter(#wOptions),
        ),
      ) as _i5.WindowsOptions);

  @override
  _i5.WebOptions get webOptions => (super.noSuchMethod(
        Invocation.getter(#webOptions),
        returnValue: _FakeWebOptions_12(
          this,
          Invocation.getter(#webOptions),
        ),
      ) as _i5.WebOptions);

  @override
  _i5.MacOsOptions get mOptions => (super.noSuchMethod(
        Invocation.getter(#mOptions),
        returnValue: _FakeMacOsOptions_13(
          this,
          Invocation.getter(#mOptions),
        ),
      ) as _i5.MacOsOptions);

  @override
  void registerListener({
    required String? key,
    required _i11.ValueChanged<String?>? listener,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #registerListener,
          [],
          {
            #key: key,
            #listener: listener,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  void unregisterListener({
    required String? key,
    required _i11.ValueChanged<String?>? listener,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #unregisterListener,
          [],
          {
            #key: key,
            #listener: listener,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  void unregisterAllListenersForKey({required String? key}) =>
      super.noSuchMethod(
        Invocation.method(
          #unregisterAllListenersForKey,
          [],
          {#key: key},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void unregisterAllListeners() => super.noSuchMethod(
        Invocation.method(
          #unregisterAllListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i7.Future<void> write({
    required String? key,
    required String? value,
    _i5.IOSOptions? iOptions,
    _i5.AndroidOptions? aOptions,
    _i5.LinuxOptions? lOptions,
    _i5.WebOptions? webOptions,
    _i5.MacOsOptions? mOptions,
    _i5.WindowsOptions? wOptions,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #write,
          [],
          {
            #key: key,
            #value: value,
            #iOptions: iOptions,
            #aOptions: aOptions,
            #lOptions: lOptions,
            #webOptions: webOptions,
            #mOptions: mOptions,
            #wOptions: wOptions,
          },
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<String?> read({
    required String? key,
    _i5.IOSOptions? iOptions,
    _i5.AndroidOptions? aOptions,
    _i5.LinuxOptions? lOptions,
    _i5.WebOptions? webOptions,
    _i5.MacOsOptions? mOptions,
    _i5.WindowsOptions? wOptions,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #read,
          [],
          {
            #key: key,
            #iOptions: iOptions,
            #aOptions: aOptions,
            #lOptions: lOptions,
            #webOptions: webOptions,
            #mOptions: mOptions,
            #wOptions: wOptions,
          },
        ),
        returnValue: _i7.Future<String?>.value(),
      ) as _i7.Future<String?>);

  @override
  _i7.Future<bool> containsKey({
    required String? key,
    _i5.IOSOptions? iOptions,
    _i5.AndroidOptions? aOptions,
    _i5.LinuxOptions? lOptions,
    _i5.WebOptions? webOptions,
    _i5.MacOsOptions? mOptions,
    _i5.WindowsOptions? wOptions,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #containsKey,
          [],
          {
            #key: key,
            #iOptions: iOptions,
            #aOptions: aOptions,
            #lOptions: lOptions,
            #webOptions: webOptions,
            #mOptions: mOptions,
            #wOptions: wOptions,
          },
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<void> delete({
    required String? key,
    _i5.IOSOptions? iOptions,
    _i5.AndroidOptions? aOptions,
    _i5.LinuxOptions? lOptions,
    _i5.WebOptions? webOptions,
    _i5.MacOsOptions? mOptions,
    _i5.WindowsOptions? wOptions,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [],
          {
            #key: key,
            #iOptions: iOptions,
            #aOptions: aOptions,
            #lOptions: lOptions,
            #webOptions: webOptions,
            #mOptions: mOptions,
            #wOptions: wOptions,
          },
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<Map<String, String>> readAll({
    _i5.IOSOptions? iOptions,
    _i5.AndroidOptions? aOptions,
    _i5.LinuxOptions? lOptions,
    _i5.WebOptions? webOptions,
    _i5.MacOsOptions? mOptions,
    _i5.WindowsOptions? wOptions,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #readAll,
          [],
          {
            #iOptions: iOptions,
            #aOptions: aOptions,
            #lOptions: lOptions,
            #webOptions: webOptions,
            #mOptions: mOptions,
            #wOptions: wOptions,
          },
        ),
        returnValue: _i7.Future<Map<String, String>>.value(<String, String>{}),
      ) as _i7.Future<Map<String, String>>);

  @override
  _i7.Future<void> deleteAll({
    _i5.IOSOptions? iOptions,
    _i5.AndroidOptions? aOptions,
    _i5.LinuxOptions? lOptions,
    _i5.WebOptions? webOptions,
    _i5.MacOsOptions? mOptions,
    _i5.WindowsOptions? wOptions,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteAll,
          [],
          {
            #iOptions: iOptions,
            #aOptions: aOptions,
            #lOptions: lOptions,
            #webOptions: webOptions,
            #mOptions: mOptions,
            #wOptions: wOptions,
          },
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  _i7.Future<bool?> isCupertinoProtectedDataAvailable() => (super.noSuchMethod(
        Invocation.method(
          #isCupertinoProtectedDataAvailable,
          [],
        ),
        returnValue: _i7.Future<bool?>.value(),
      ) as _i7.Future<bool?>);
}

/// A class which mocks [GoogleSignIn].
///
/// See the documentation for Mockito's code generation for more information.
class MockGoogleSignIn extends _i1.Mock implements _i6.GoogleSignIn {
  MockGoogleSignIn() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i12.SignInOption get signInOption => (super.noSuchMethod(
        Invocation.getter(#signInOption),
        returnValue: _i12.SignInOption.standard,
      ) as _i12.SignInOption);

  @override
  List<String> get scopes => (super.noSuchMethod(
        Invocation.getter(#scopes),
        returnValue: <String>[],
      ) as List<String>);

  @override
  bool get forceCodeForRefreshToken => (super.noSuchMethod(
        Invocation.getter(#forceCodeForRefreshToken),
        returnValue: false,
      ) as bool);

  @override
  _i7.Stream<_i6.GoogleSignInAccount?> get onCurrentUserChanged =>
      (super.noSuchMethod(
        Invocation.getter(#onCurrentUserChanged),
        returnValue: _i7.Stream<_i6.GoogleSignInAccount?>.empty(),
      ) as _i7.Stream<_i6.GoogleSignInAccount?>);

  @override
  _i7.Future<_i6.GoogleSignInAccount?> signInSilently({
    bool? suppressErrors = true,
    bool? reAuthenticate = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInSilently,
          [],
          {
            #suppressErrors: suppressErrors,
            #reAuthenticate: reAuthenticate,
          },
        ),
        returnValue: _i7.Future<_i6.GoogleSignInAccount?>.value(),
      ) as _i7.Future<_i6.GoogleSignInAccount?>);

  @override
  _i7.Future<bool> isSignedIn() => (super.noSuchMethod(
        Invocation.method(
          #isSignedIn,
          [],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<_i6.GoogleSignInAccount?> signIn() => (super.noSuchMethod(
        Invocation.method(
          #signIn,
          [],
        ),
        returnValue: _i7.Future<_i6.GoogleSignInAccount?>.value(),
      ) as _i7.Future<_i6.GoogleSignInAccount?>);

  @override
  _i7.Future<_i6.GoogleSignInAccount?> signOut() => (super.noSuchMethod(
        Invocation.method(
          #signOut,
          [],
        ),
        returnValue: _i7.Future<_i6.GoogleSignInAccount?>.value(),
      ) as _i7.Future<_i6.GoogleSignInAccount?>);

  @override
  _i7.Future<_i6.GoogleSignInAccount?> disconnect() => (super.noSuchMethod(
        Invocation.method(
          #disconnect,
          [],
        ),
        returnValue: _i7.Future<_i6.GoogleSignInAccount?>.value(),
      ) as _i7.Future<_i6.GoogleSignInAccount?>);

  @override
  _i7.Future<bool> requestScopes(List<String>? scopes) => (super.noSuchMethod(
        Invocation.method(
          #requestScopes,
          [scopes],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);

  @override
  _i7.Future<bool> canAccessScopes(
    List<String>? scopes, {
    String? accessToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #canAccessScopes,
          [scopes],
          {#accessToken: accessToken},
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);
}

/// A class which mocks [GoogleSignInAccount].
///
/// See the documentation for Mockito's code generation for more information.
// ignore: must_be_immutable
class MockGoogleSignInAccount extends _i1.Mock
    implements _i6.GoogleSignInAccount {
  MockGoogleSignInAccount() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get email => (super.noSuchMethod(
        Invocation.getter(#email),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#email),
        ),
      ) as String);

  @override
  String get id => (super.noSuchMethod(
        Invocation.getter(#id),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#id),
        ),
      ) as String);

  @override
  _i7.Future<_i6.GoogleSignInAuthentication> get authentication =>
      (super.noSuchMethod(
        Invocation.getter(#authentication),
        returnValue: _i7.Future<_i6.GoogleSignInAuthentication>.value(
            _FakeGoogleSignInAuthentication_14(
          this,
          Invocation.getter(#authentication),
        )),
      ) as _i7.Future<_i6.GoogleSignInAuthentication>);

  @override
  _i7.Future<Map<String, String>> get authHeaders => (super.noSuchMethod(
        Invocation.getter(#authHeaders),
        returnValue: _i7.Future<Map<String, String>>.value(<String, String>{}),
      ) as _i7.Future<Map<String, String>>);

  @override
  _i7.Future<void> clearAuthCache() => (super.noSuchMethod(
        Invocation.method(
          #clearAuthCache,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);
}

/// A class which mocks [GoogleSignInAuthentication].
///
/// See the documentation for Mockito's code generation for more information.
class MockGoogleSignInAuthentication extends _i1.Mock
    implements _i6.GoogleSignInAuthentication {
  MockGoogleSignInAuthentication() {
    _i1.throwOnMissingStub(this);
  }
}
