// Mocks generated by <PERSON><PERSON><PERSON> 5.4.4 from annotations
// in darvis_app/test/smart_capture_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;
import 'dart:io' as _i5;

import 'package:darvis_app/services/api_service.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [ApiService].
///
/// See the documentation for <PERSON><PERSON><PERSON>'s code generation for more information.
class MockApiService extends _i1.Mock implements _i2.ApiService {
  MockApiService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<Map<String, dynamic>> login({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #login,
          [],
          {
            #email: email,
            #password: password,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> register({
    required String? email,
    required String? password,
    required String? name,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #register,
          [],
          {
            #email: email,
            #password: password,
            #name: name,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> refreshToken(String? refreshToken) =>
      (super.noSuchMethod(
        Invocation.method(
          #refreshToken,
          [refreshToken],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> exchangeFirebaseToken(String? idToken) =>
      (super.noSuchMethod(
        Invocation.method(
          #exchangeFirebaseToken,
          [idToken],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> syncUserProfile(
          Map<String, dynamic>? profileData) =>
      (super.noSuchMethod(
        Invocation.method(
          #syncUserProfile,
          [profileData],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> updateUserSession() => (super.noSuchMethod(
        Invocation.method(
          #updateUserSession,
          [],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getUserSessionData() => (super.noSuchMethod(
        Invocation.method(
          #getUserSessionData,
          [],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> createNotification(
          dynamic notificationDto) =>
      (super.noSuchMethod(
        Invocation.method(
          #createNotification,
          [notificationDto],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> updateNotificationSettings(
          dynamic settingsDto) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateNotificationSettings,
          [settingsDto],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> markNotificationAsInteracted(
          String? notificationId) =>
      (super.noSuchMethod(
        Invocation.method(
          #markNotificationAsInteracted,
          [notificationId],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getUserNotifications({
    int? page = 1,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserNotifications,
          [],
          {
            #page: page,
            #limit: limit,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> updateUserProfile(dynamic profileDto) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateUserProfile,
          [profileDto],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> updateProfilePicture(
          dynamic updateRequest) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateProfilePicture,
          [updateRequest],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getUserProfile() => (super.noSuchMethod(
        Invocation.method(
          #getUserProfile,
          [],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> deleteProfilePicture() =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteProfilePicture,
          [],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getDashboardData() => (super.noSuchMethod(
        Invocation.method(
          #getDashboardData,
          [],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> sendMessage({
    required String? message,
    required String? conversationId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendMessage,
          [],
          {
            #message: message,
            #conversationId: conversationId,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<List<Map<String, dynamic>>> getConversations() =>
      (super.noSuchMethod(
        Invocation.method(
          #getConversations,
          [],
        ),
        returnValue: _i3.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
      ) as _i3.Future<List<Map<String, dynamic>>>);

  @override
  _i3.Future<Map<String, dynamic>> createConversation({
    required String? title,
    String? description,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createConversation,
          [],
          {
            #title: title,
            #description: description,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<void> deleteConversation(String? conversationId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteConversation,
          [conversationId],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<Map<String, dynamic>> createTask({
    required String? title,
    required String? description,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createTask,
          [],
          {
            #title: title,
            #description: description,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<List<Map<String, dynamic>>> getTasks() => (super.noSuchMethod(
        Invocation.method(
          #getTasks,
          [],
        ),
        returnValue: _i3.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
      ) as _i3.Future<List<Map<String, dynamic>>>);

  @override
  _i3.Future<Map<String, dynamic>> updateTask({
    required String? taskId,
    Map<String, dynamic>? updates,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateTask,
          [],
          {
            #taskId: taskId,
            #updates: updates,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> createNote({
    required String? title,
    required String? content,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createNote,
          [],
          {
            #title: title,
            #content: content,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<List<Map<String, dynamic>>> getNotes() => (super.noSuchMethod(
        Invocation.method(
          #getNotes,
          [],
        ),
        returnValue: _i3.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
      ) as _i3.Future<List<Map<String, dynamic>>>);

  @override
  _i3.Future<Map<String, dynamic>> updateNote({
    required String? noteId,
    Map<String, dynamic>? updates,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateNote,
          [],
          {
            #noteId: noteId,
            #updates: updates,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> deleteNote(String? noteId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteNote,
          [noteId],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> deleteTask(String? taskId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteTask,
          [taskId],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> createCalendarEvent({
    required String? title,
    required String? description,
    required DateTime? startTime,
    required DateTime? endTime,
    String? type,
    bool? isAllDay,
    String? location,
    List<String>? attendees,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createCalendarEvent,
          [],
          {
            #title: title,
            #description: description,
            #startTime: startTime,
            #endTime: endTime,
            #type: type,
            #isAllDay: isAllDay,
            #location: location,
            #attendees: attendees,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<List<Map<String, dynamic>>> getCalendarEvents({
    DateTime? startDate,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCalendarEvents,
          [],
          {
            #startDate: startDate,
            #endDate: endDate,
          },
        ),
        returnValue: _i3.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
      ) as _i3.Future<List<Map<String, dynamic>>>);

  @override
  _i3.Future<Map<String, dynamic>> updateCalendarEvent({
    required String? eventId,
    Map<String, dynamic>? updates,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateCalendarEvent,
          [],
          {
            #eventId: eventId,
            #updates: updates,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> deleteCalendarEvent(String? eventId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteCalendarEvent,
          [eventId],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> searchNotes({
    required String? query,
    String? tag,
    int? page = 1,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchNotes,
          [],
          {
            #query: query,
            #tag: tag,
            #page: page,
            #limit: limit,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> searchTasks({
    required String? query,
    String? status,
    String? priority,
    int? page = 1,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchTasks,
          [],
          {
            #query: query,
            #status: status,
            #priority: priority,
            #page: page,
            #limit: limit,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> searchCalendarEvents({
    required String? query,
    String? type,
    DateTime? startDate,
    DateTime? endDate,
    int? page = 1,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchCalendarEvents,
          [],
          {
            #query: query,
            #type: type,
            #startDate: startDate,
            #endDate: endDate,
            #page: page,
            #limit: limit,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> createContact({
    required String? name,
    required String? phone,
    String? email,
    String? location,
    String? metAt,
    Map<String, String>? socialMedia,
    String? memoryPrompt,
    String? imagePath,
    String? imagePublicId,
    String? deviceSyncStatus = r'disabled',
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createContact,
          [],
          {
            #name: name,
            #phone: phone,
            #email: email,
            #location: location,
            #metAt: metAt,
            #socialMedia: socialMedia,
            #memoryPrompt: memoryPrompt,
            #imagePath: imagePath,
            #imagePublicId: imagePublicId,
            #deviceSyncStatus: deviceSyncStatus,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getContacts({
    String? search,
    String? platform,
    int? limit = 50,
    int? offset = 0,
    String? sortBy = r'name',
    String? sortOrder = r'asc',
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getContacts,
          [],
          {
            #search: search,
            #platform: platform,
            #limit: limit,
            #offset: offset,
            #sortBy: sortBy,
            #sortOrder: sortOrder,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getContact(String? contactId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getContact,
          [contactId],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> updateContact({
    required String? contactId,
    String? name,
    String? phone,
    String? email,
    String? location,
    String? metAt,
    Map<String, String>? socialMedia,
    String? memoryPrompt,
    String? imagePath,
    String? imagePublicId,
    String? deviceSyncStatus,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateContact,
          [],
          {
            #contactId: contactId,
            #name: name,
            #phone: phone,
            #email: email,
            #location: location,
            #metAt: metAt,
            #socialMedia: socialMedia,
            #memoryPrompt: memoryPrompt,
            #imagePath: imagePath,
            #imagePublicId: imagePublicId,
            #deviceSyncStatus: deviceSyncStatus,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> deleteContact(String? contactId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteContact,
          [contactId],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<String> getContactVCF(String? contactId) => (super.noSuchMethod(
        Invocation.method(
          #getContactVCF,
          [contactId],
        ),
        returnValue: _i3.Future<String>.value(_i4.dummyValue<String>(
          this,
          Invocation.method(
            #getContactVCF,
            [contactId],
          ),
        )),
      ) as _i3.Future<String>);

  @override
  _i3.Future<Map<String, dynamic>> bulkExportContacts({
    required List<String>? contactIds,
    String? format = r'vcf',
    bool? includeImages = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #bulkExportContacts,
          [],
          {
            #contactIds: contactIds,
            #format: format,
            #includeImages: includeImages,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> exportAllContacts({
    String? format = r'vcf',
    bool? includeImages = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #exportAllContacts,
          [],
          {
            #format: format,
            #includeImages: includeImages,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> updateDeviceSync({
    required String? contactId,
    String? deviceContactId,
    required String? syncStatus,
    String? syncError,
    Map<String, String>? deviceInfo,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateDeviceSync,
          [],
          {
            #contactId: contactId,
            #deviceContactId: deviceContactId,
            #syncStatus: syncStatus,
            #syncError: syncError,
            #deviceInfo: deviceInfo,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getContactsSyncStatus() =>
      (super.noSuchMethod(
        Invocation.method(
          #getContactsSyncStatus,
          [],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> bulkDeviceSync({
    required List<String>? contactIds,
    required Map<String, String>? deviceInfo,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #bulkDeviceSync,
          [],
          {
            #contactIds: contactIds,
            #deviceInfo: deviceInfo,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> searchContacts({
    required String? query,
    String? fields,
    String? platform,
    String? dateFrom,
    String? dateTo,
    int? limit = 20,
    int? offset = 0,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchContacts,
          [],
          {
            #query: query,
            #fields: fields,
            #platform: platform,
            #dateFrom: dateFrom,
            #dateTo: dateTo,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> captureContent({
    required String? url,
    String? title,
    String? description,
    List<String>? tags,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #captureContent,
          [],
          {
            #url: url,
            #title: title,
            #description: description,
            #tags: tags,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> uploadAndProcessImage(_i5.File? image) =>
      (super.noSuchMethod(
        Invocation.method(
          #uploadAndProcessImage,
          [image],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getCapturedContent({
    String? search,
    String? contentType,
    String? status,
    List<String>? tags,
    int? limit = 20,
    int? offset = 0,
    String? sortBy = r'created_at',
    String? sortOrder = r'desc',
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCapturedContent,
          [],
          {
            #search: search,
            #contentType: contentType,
            #status: status,
            #tags: tags,
            #limit: limit,
            #offset: offset,
            #sortBy: sortBy,
            #sortOrder: sortOrder,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getCapturedContentById(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCapturedContentById,
          [id],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> updateCapturedContent({
    required String? id,
    String? title,
    String? summary,
    List<String>? tags,
    String? status,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateCapturedContent,
          [],
          {
            #id: id,
            #title: title,
            #summary: summary,
            #tags: tags,
            #status: status,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<void> deleteCapturedContent(String? id) => (super.noSuchMethod(
        Invocation.method(
          #deleteCapturedContent,
          [id],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<Map<String, dynamic>> bulkDeleteCapturedContent(
          List<String>? ids) =>
      (super.noSuchMethod(
        Invocation.method(
          #bulkDeleteCapturedContent,
          [ids],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> searchCapturedContent({
    required String? query,
    String? contentType,
    List<String>? tags,
    String? dateFrom,
    String? dateTo,
    int? limit = 20,
    int? offset = 0,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchCapturedContent,
          [],
          {
            #query: query,
            #contentType: contentType,
            #tags: tags,
            #dateFrom: dateFrom,
            #dateTo: dateTo,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getProcessingStatus(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #getProcessingStatus,
          [id],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> retryContentProcessing(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #retryContentProcessing,
          [id],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getCapturedContentTags() =>
      (super.noSuchMethod(
        Invocation.method(
          #getCapturedContentTags,
          [],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getCapturedContentAnalytics({
    String? dateFrom,
    String? dateTo,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCapturedContentAnalytics,
          [],
          {
            #dateFrom: dateFrom,
            #dateTo: dateTo,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> importContentToNotes({
    required String? contentId,
    String? noteTitle,
    String? noteContent,
    List<String>? tags,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #importContentToNotes,
          [],
          {
            #contentId: contentId,
            #noteTitle: noteTitle,
            #noteContent: noteContent,
            #tags: tags,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> addContentToChatContext({
    required String? contentId,
    String? conversationId,
    String? contextMessage,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addContentToChatContext,
          [],
          {
            #contentId: contentId,
            #conversationId: conversationId,
            #contextMessage: contextMessage,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> createEventFromContent({
    required String? contentId,
    required String? title,
    required DateTime? startTime,
    required DateTime? endTime,
    String? description,
    String? location,
    List<String>? attendees,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createEventFromContent,
          [],
          {
            #contentId: contentId,
            #title: title,
            #startTime: startTime,
            #endTime: endTime,
            #description: description,
            #location: location,
            #attendees: attendees,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getContactAnalytics() => (super.noSuchMethod(
        Invocation.method(
          #getContactAnalytics,
          [],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getSocialPlatforms() => (super.noSuchMethod(
        Invocation.method(
          #getSocialPlatforms,
          [],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getSocialPlatform(String? platform) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSocialPlatform,
          [platform],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> validateSocialUsername({
    required String? platform,
    required String? username,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #validateSocialUsername,
          [],
          {
            #platform: platform,
            #username: username,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> verifySocialProfile({
    required String? contactId,
    required String? platform,
    required String? username,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifySocialProfile,
          [],
          {
            #contactId: contactId,
            #platform: platform,
            #username: username,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);
}
