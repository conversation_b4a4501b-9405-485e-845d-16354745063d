package com.drix.app

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import android.webkit.MimeTypeMap

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.drix.app/share"
    private var methodChannel: MethodChannel? = null
    private var pendingSharedData: Map<String, Any>? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
        methodChannel?.setMethodCallHandler { call, result ->
            when (call.method) {
                "getInitialSharedData" -> {
                    result.success(pendingSharedData)
                    pendingSharedData = null // Clear after sending
                }
                "copySharedUriToCache" -> {
                    val uriString = (call.arguments as? Map<*, *>)?.get("uri") as? String
                    if (uriString.isNullOrEmpty()) {
                        result.error("INVALID_ARG", "Missing uri argument", null)
                    } else {
                        try {
                            val filePath = copyUriToCache(Uri.parse(uriString))
                            if (filePath != null) {
                                result.success(filePath)
                            } else {
                                result.error("COPY_FAILED", "Failed to copy URI to cache", null)
                            }
                        } catch (e: Exception) {
                            result.error("EXCEPTION", e.message, null)
                        }
                    }
                }
                else -> result.notImplemented()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        handleIntent(intent)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent?) {
        if (intent == null) return

        when (intent.action) {
            Intent.ACTION_SEND -> {
                if (intent.type?.startsWith("text/") == true) {
                    handleTextShare(intent)
                } else if (intent.type?.startsWith("image/") == true) {
                    handleImageShare(intent)
                }
            }
            Intent.ACTION_SEND_MULTIPLE -> {
                if (intent.type?.startsWith("image/") == true) {
                    handleMultipleImagesShare(intent)
                }
            }
        }
    }

    private fun handleTextShare(intent: Intent) {
        val sharedText = intent.getStringExtra(Intent.EXTRA_TEXT)
        val sharedSubject = intent.getStringExtra(Intent.EXTRA_SUBJECT)

        if (!sharedText.isNullOrEmpty()) {
            val sharedData = mapOf(
                "type" to "text",
                "content" to sharedText,
                "subject" to (sharedSubject ?: ""),
                "timestamp" to System.currentTimeMillis()
            )

            if (methodChannel != null) {
                methodChannel?.invokeMethod("handleSharedContent", sharedData)
            } else {
                pendingSharedData = sharedData
            }
        }
    }

    private fun handleImageShare(intent: Intent) {
        val imageUri = intent.getParcelableExtra<Uri>(Intent.EXTRA_STREAM)

        if (imageUri != null) {
            val cachedPath = copyUriToCache(imageUri)
            val sharedData = mutableMapOf<String, Any>(
                "type" to "image",
                "uri" to imageUri.toString(),
                "timestamp" to System.currentTimeMillis()
            )
            if (cachedPath != null) {
                sharedData["filePath"] = cachedPath
            }

            if (methodChannel != null) {
                methodChannel?.invokeMethod("handleSharedContent", sharedData)
            } else {
                pendingSharedData = sharedData
            }
        }
    }

    private fun handleMultipleImagesShare(intent: Intent) {
        val imageUris = intent.getParcelableArrayListExtra<Uri>(Intent.EXTRA_STREAM)

        if (!imageUris.isNullOrEmpty()) {
            val uriStrings = imageUris.map { it.toString() }
            val filePaths = mutableListOf<String>()
            for (u in imageUris) {
                val p = copyUriToCache(u)
                if (p != null) filePaths.add(p)
            }

            val sharedData = mutableMapOf<String, Any>(
                "type" to "images",
                "uris" to uriStrings,
                "timestamp" to System.currentTimeMillis()
            )
            if (filePaths.isNotEmpty()) {
                sharedData["filePaths"] = filePaths
            }

            if (methodChannel != null) {
                methodChannel?.invokeMethod("handleSharedContent", sharedData)
            } else {
                pendingSharedData = sharedData
            }
        }
    }

    private fun copyUriToCache(uri: Uri): String? {
        return try {
            val resolver = applicationContext.contentResolver
            val mimeType = resolver.getType(uri)
            val ext = MimeTypeMap.getSingleton().getExtensionFromMimeType(mimeType ?: "") ?: "jpg"

            val cacheDir: File = applicationContext.cacheDir
            val outFile = File.createTempFile("shared_", ".${ext}", cacheDir)

            resolver.openInputStream(uri).use { inputStream: InputStream? ->
                if (inputStream == null) return null
                FileOutputStream(outFile).use { outputStream ->
                    val buffer = ByteArray(8 * 1024)
                    var bytesRead: Int
                    while (true) {
                        bytesRead = inputStream.read(buffer)
                        if (bytesRead == -1) break
                        outputStream.write(buffer, 0, bytesRead)
                    }
                    outputStream.flush()
                }
            }
            outFile.absolutePath
        } catch (e: Exception) {
            null
        }
    }
}
