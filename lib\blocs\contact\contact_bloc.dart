import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/contact_models.dart';
import '../../services/contact_service.dart';
import 'contact_event.dart';
import 'contact_state.dart';

/// BLoC for managing contacts with offline-first approach
class ContactBloc extends Bloc<ContactEvent, ContactState> {
  final ContactService _contactService;

  ContactBloc({
    required ContactService contactService,
  })  : _contactService = contactService,
        super(const ContactInitial()) {
    on<LoadContacts>(_onLoadContacts);
    on<SearchContacts>(_onSearchContacts);
    on<CreateContact>(_onCreateContact);
    on<UpdateContact>(_onUpdateContact);
    on<DeleteContact>(_onDeleteContact);
    on<BulkDeleteContacts>(_onBulkDeleteContacts);
    on<ExportContactAsVcf>(_onExportContactAsVcf);
    on<ExportContactsAsVcf>(_onExportContactsAsVcf);
    on<ImportContactsFromDevice>(_onImportContactsFromDevice);
    on<SyncContacts>(_onSyncContacts);
    on<AddSocialMediaProfile>(_onAddSocialMediaProfile);
    on<RemoveSocialMediaProfile>(_onRemoveSocialMediaProfile);
    on<OpenSocialMediaProfile>(_onOpenSocialMediaProfile);
    on<FilterContactsByTag>(_onFilterContactsByTag);
    on<SortContacts>(_onSortContacts);
  }

  Future<void> _onLoadContacts(
    LoadContacts event,
    Emitter<ContactState> emit,
  ) async {
    emit(const ContactLoading());
    
    try {
      final contacts = await _contactService.getAllContacts();
      emit(ContactsLoaded(
        contacts: contacts,
        filteredContacts: contacts,
      ));
    } catch (e) {
      emit(ContactError(message: 'Failed to load contacts: $e'));
    }
  }

  Future<void> _onSearchContacts(
    SearchContacts event,
    Emitter<ContactState> emit,
  ) async {
    final currentState = state;
    if (currentState is ContactsLoaded) {
      emit(currentState.copyWith(isSearching: true));
      
      try {
        final filteredContacts = await _contactService.searchContacts(event.query);
        emit(currentState.copyWith(
          filteredContacts: filteredContacts,
          searchQuery: event.query.isEmpty ? null : event.query,
          isSearching: false,
        ));
      } catch (e) {
        emit(ContactError(message: 'Failed to search contacts: $e'));
      }
    }
  }

  Future<void> _onCreateContact(
    CreateContact event,
    Emitter<ContactState> emit,
  ) async {
    emit(const ContactOperationInProgress('Creating contact...'));
    
    try {
      final createdContact = await _contactService.createContact(event.contact);
      emit(ContactCreated(createdContact));
      
      // Reload contacts
      add(const LoadContacts());
    } catch (e) {
      emit(ContactError(message: 'Failed to create contact: $e'));
    }
  }

  Future<void> _onUpdateContact(
    UpdateContact event,
    Emitter<ContactState> emit,
  ) async {
    emit(const ContactOperationInProgress('Updating contact...'));
    
    try {
      final updatedContact = await _contactService.updateContact(event.contact);
      emit(ContactUpdated(updatedContact));
      
      // Reload contacts
      add(const LoadContacts());
    } catch (e) {
      emit(ContactError(message: 'Failed to update contact: $e'));
    }
  }

  Future<void> _onDeleteContact(
    DeleteContact event,
    Emitter<ContactState> emit,
  ) async {
    emit(const ContactOperationInProgress('Deleting contact...'));
    
    try {
      final success = await _contactService.deleteContact(event.contactId);
      if (success) {
        emit(ContactDeleted(event.contactId));
        
        // Reload contacts
        add(const LoadContacts());
      } else {
        emit(const ContactError(message: 'Failed to delete contact'));
      }
    } catch (e) {
      emit(ContactError(message: 'Failed to delete contact: $e'));
    }
  }

  Future<void> _onBulkDeleteContacts(
    BulkDeleteContacts event,
    Emitter<ContactState> emit,
  ) async {
    emit(const ContactOperationInProgress('Deleting contacts...'));
    
    try {
      final deletedCount = await _contactService.bulkDeleteContacts(event.contactIds);
      emit(ContactsBulkDeleted(
        contactIds: event.contactIds,
        deletedCount: deletedCount,
      ));
      
      // Reload contacts
      add(const LoadContacts());
    } catch (e) {
      emit(ContactError(message: 'Failed to delete contacts: $e'));
    }
  }

  Future<void> _onExportContactAsVcf(
    ExportContactAsVcf event,
    Emitter<ContactState> emit,
  ) async {
    emit(const ContactOperationInProgress('Exporting contact...'));
    
    try {
      final filePath = await _contactService.exportContactAsVcf(event.contact);
      emit(ContactVcfExported(
        filePath: filePath,
        contactCount: 1,
      ));
    } catch (e) {
      emit(ContactError(message: 'Failed to export contact: $e'));
    }
  }

  Future<void> _onExportContactsAsVcf(
    ExportContactsAsVcf event,
    Emitter<ContactState> emit,
  ) async {
    emit(const ContactOperationInProgress('Exporting contacts...'));
    
    try {
      final filePath = await _contactService.exportContactsAsVcf(event.contacts);
      emit(ContactVcfExported(
        filePath: filePath,
        contactCount: event.contacts.length,
      ));
    } catch (e) {
      emit(ContactError(message: 'Failed to export contacts: $e'));
    }
  }

  Future<void> _onImportContactsFromDevice(
    ImportContactsFromDevice event,
    Emitter<ContactState> emit,
  ) async {
    emit(const ContactOperationInProgress('Importing contacts...'));
    
    try {
      final result = await _contactService.importContactsFromDevice();
      emit(ContactsImportedFromDevice(
        importedCount: result['imported'] ?? 0,
        skippedCount: result['skipped'] ?? 0,
      ));
      
      // Reload contacts
      add(const LoadContacts());
    } catch (e) {
      if (e.toString().contains('permission')) {
        emit(const ContactPermissionError(
          message: 'Contact permission required to import contacts',
          permissionType: 'contacts',
        ));
      } else {
        emit(ContactError(message: 'Failed to import contacts: $e'));
      }
    }
  }

  Future<void> _onSyncContacts(
    SyncContacts event,
    Emitter<ContactState> emit,
  ) async {
    emit(const ContactOperationInProgress('Syncing contacts...'));
    
    try {
      final syncedCount = await _contactService.syncContacts();
      emit(ContactsSynced(
        syncedCount: syncedCount,
        syncTime: DateTime.now(),
      ));
      
      // Reload contacts
      add(const LoadContacts());
    } catch (e) {
      if (e.toString().contains('network') || e.toString().contains('connection')) {
        emit(ContactNetworkError('Network error: Please check your connection'));
      } else {
        emit(ContactError(message: 'Failed to sync contacts: $e'));
      }
    }
  }

  Future<void> _onAddSocialMediaProfile(
    AddSocialMediaProfile event,
    Emitter<ContactState> emit,
  ) async {
    emit(const ContactOperationInProgress('Adding social media profile...'));
    
    try {
      final success = await _contactService.addSocialMediaProfile(
        contactId: event.contactId,
        platform: event.platform,
        username: event.username,
      );
      
      if (success) {
        // Reload contacts to show updated data
        add(const LoadContacts());
      } else {
        emit(const ContactError(message: 'Failed to add social media profile'));
      }
    } catch (e) {
      emit(ContactError(message: 'Failed to add social media profile: $e'));
    }
  }

  Future<void> _onRemoveSocialMediaProfile(
    RemoveSocialMediaProfile event,
    Emitter<ContactState> emit,
  ) async {
    emit(const ContactOperationInProgress('Removing social media profile...'));
    
    try {
      final success = await _contactService.removeSocialMediaProfile(
        contactId: event.contactId,
        platform: event.platform,
      );
      
      if (success) {
        // Reload contacts to show updated data
        add(const LoadContacts());
      } else {
        emit(const ContactError(message: 'Failed to remove social media profile'));
      }
    } catch (e) {
      emit(ContactError(message: 'Failed to remove social media profile: $e'));
    }
  }

  Future<void> _onOpenSocialMediaProfile(
    OpenSocialMediaProfile event,
    Emitter<ContactState> emit,
  ) async {
    try {
      final success = await _contactService.openSocialMediaProfile(
        platform: event.platform,
        username: event.username,
      );
      
      if (success) {
        emit(SocialMediaProfileOpened(
          platform: event.platform,
          username: event.username,
        ));
      } else {
        emit(ContactError(
          message: 'Failed to open ${event.platform} profile for ${event.username}',
        ));
      }
    } catch (e) {
      emit(ContactError(message: 'Failed to open social media profile: $e'));
    }
  }

  Future<void> _onFilterContactsByTag(
    FilterContactsByTag event,
    Emitter<ContactState> emit,
  ) async {
    final currentState = state;
    if (currentState is ContactsLoaded) {
      try {
        List<ContactModel> filteredContacts;
        
        if (event.tag == null || event.tag!.isEmpty) {
          // Show all contacts
          filteredContacts = currentState.contacts;
        } else {
          // Filter by tag
          filteredContacts = await _contactService.getContactsByTag(event.tag!);
        }
        
        emit(currentState.copyWith(
          filteredContacts: filteredContacts,
          selectedTag: event.tag,
        ));
      } catch (e) {
        emit(ContactError(message: 'Failed to filter contacts: $e'));
      }
    }
  }

  Future<void> _onSortContacts(
    SortContacts event,
    Emitter<ContactState> emit,
  ) async {
    final currentState = state;
    if (currentState is ContactsLoaded) {
      final sortedContacts = List<ContactModel>.from(currentState.filteredContacts);
      
      switch (event.sortOption) {
        case ContactSortOption.nameAsc:
          sortedContacts.sort((a, b) => a.name.compareTo(b.name));
          break;
        case ContactSortOption.nameDesc:
          sortedContacts.sort((a, b) => b.name.compareTo(a.name));
          break;
        case ContactSortOption.dateCreatedAsc:
          sortedContacts.sort((a, b) => a.createdAt.compareTo(b.createdAt));
          break;
        case ContactSortOption.dateCreatedDesc:
          sortedContacts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          break;
        case ContactSortOption.lastContactedAsc:
          sortedContacts.sort((a, b) {
            if (a.lastContacted == null && b.lastContacted == null) return 0;
            if (a.lastContacted == null) return 1;
            if (b.lastContacted == null) return -1;
            return a.lastContacted!.compareTo(b.lastContacted!);
          });
          break;
        case ContactSortOption.lastContactedDesc:
          sortedContacts.sort((a, b) {
            if (a.lastContacted == null && b.lastContacted == null) return 0;
            if (a.lastContacted == null) return 1;
            if (b.lastContacted == null) return -1;
            return b.lastContacted!.compareTo(a.lastContacted!);
          });
          break;
      }
      
      emit(currentState.copyWith(
        filteredContacts: sortedContacts,
        sortOption: event.sortOption,
      ));
    }
  }
}
