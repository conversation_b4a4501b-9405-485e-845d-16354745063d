name: darvis_app
description: "Drix AI Assistant - The Conversational AI Second Brain"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.1.0 <4.0.0'
  flutter: ">=3.13.0"

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_bloc: ^8.1.3
  bloc: ^8.1.2
  equatable: ^2.0.5

  # UI & Widgets
  flutter_svg: ^2.0.7
  image_picker: ^1.0.4
  lottie: ^3.1.2

  # Notifications
  flutter_local_notifications: ^17.2.2
  timezone: ^0.9.4

  # Image Processing & Cloud Storage
  http: ^1.2.2
  crypto: ^3.0.5
  path: ^1.9.0
  path_provider: ^2.1.4

  # API Communication
  dio: ^5.3.2
  dio_smart_retry: ^7.0.1


  # Real-time Voice
  livekit_client: ^2.4.8

  # Authentication & Firebase
  firebase_core: ^2.17.0
  firebase_auth: ^4.10.1
  firebase_messaging: ^14.6.9
  firebase_crashlytics: ^3.4.8
  firebase_performance: ^0.9.3+8
  google_sign_in: ^6.1.5

  # Local Storage & Offline - Updated for Android namespace compatibility
  isar: ^3.1.0+1
  isar_flutter_libs: ^3.1.0+1

  # Security
  flutter_secure_storage: ^9.0.0
  encrypt: ^5.0.1
  shared_preferences: ^2.2.2

  # Service Location
  get_it: ^7.6.4

  # Performance & Caching
  cached_network_image: ^3.3.0

  # UI & Icons
  cupertino_icons: ^1.0.2

  #calendar table 
  table_calendar: ^3.2.0

  intl: ^0.20.0

  #contact screen help
  url_launcher: ^6.3.1

  # Device Integration for Phase 3
  permission_handler: ^11.3.1
  flutter_contacts: ^1.1.9
  vcf_dart: ^1.0.2
  share_plus: ^11.1.0
  # android_intent_plus: ^5.0.2  # Removed; using url_launcher schemes instead
  # receive_sharing_intent: ^1.8.1  # Temporarily disabled due to namespace issues
  # device_apps: ^2.2.0  # Temporarily disabled due to namespace issues

dev_dependencies:
  flutter_test:
    sdk: flutter

  change_app_package_name: ^1.5.0

  # Testing
  # bloc_test: ^9.1.4  # Temporarily disabled due to analyzer version conflict with isar_generator
  mocktail: ^1.0.0
  mockito: ^5.4.4
  golden_toolkit: ^0.15.0

  # Build Tools
  build_runner: ^2.4.7
  yaml: ^3.1.2  # For design token generation
  isar_generator: ^3.1.0

  # Linting
  flutter_lints: ^2.0.0

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/icons/categories/

  # Fonts
  fonts:
    - family: Outfit
      fonts:
        - asset: assets/fonts/Outfit-Regular.ttf
        - asset: assets/fonts/Outfit-SemiBold.ttf
          weight: 600
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
