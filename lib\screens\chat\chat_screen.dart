import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:get_it/get_it.dart';
import 'package:darvis_app/utils/design_tokens.dart';
import 'package:darvis_app/services/navigation_service.dart';
import 'package:darvis_app/services/chat_service.dart';
import 'package:darvis_app/models/chat_models.dart';

// --- Reusable Asset Path Constants ---
const String _kChatMenuIcon = 'assets/icons/chat_menu.svg';
const String _kNewChatIcon = 'assets/icons/new_chat.svg';
const String _kDarvisLogo = 'assets/images/darvis_main.PNG';
const String _kDarvisChatIcon = 'assets/icons/darvis_chat.svg';
const String _kCopyIcon = 'assets/icons/copy_inactive.svg';
const String _kSearchActiveIcon = 'assets/icons/search_active.svg';
const String _kSearchInactiveIcon = 'assets/icons/search_inactive.svg';
const String _kVoiceModeIcon = 'assets/icons/voice_mode.svg';
const String _kPlusIcon = 'assets/icons/addimage.svg';
const String _kSendIcon = 'assets/icons/send_icon.svg';
const String _kHomeIcon = 'assets/icons/home_chat.svg';
const String _kRemoveImageIcon = 'assets/icons/remove_image.svg';
const String _kUserImageExample = 'assets/images/profile_pic.png';

// --- Mock Data Models for Testing ---
// In a real app, these would be in your /models folder and come from the BLoC.
enum MessageSender { user, darvis }

class LocalChatMessage {
  final String text;
  final MessageSender sender;
  final String? imagePath; // For image messages

  LocalChatMessage({required this.text, required this.sender, this.imagePath});
}

/// ChatScreen: The main conversational interface.
class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with TickerProviderStateMixin {
  // Key to control the Scaffold's side menu (Drawer).
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // State tracking for drawer keyboard interaction fix
  bool _wasTextFieldFocusedBeforeDrawer = false;
  bool _isDrawerOpen = false;
  final GlobalKey<_BottomInputBarState> _bottomInputKey = GlobalKey<_BottomInputBarState>();

  // Chat service and conversation
  late final ChatService _chatService;
  String _currentConversationId = 'default_conversation';
  List<ChatMessage> _realMessages = [];
  bool _isLoading = false;

  // Mock data for chat history - this would come from the ChatBloc state.
  final List<LocalChatMessage> _messages = [
    LocalChatMessage(
        sender: MessageSender.darvis,
        text: 'Hey David, happy to be at your service'),
    LocalChatMessage(
        sender: MessageSender.user,
        text: 'write a very short story about a rat'),
    LocalChatMessage(
        sender: MessageSender.darvis,
        text:
            'Once, a clever rat named Milo lived in a busy kitchen. One night, he outsmarted a cat by hiding in a cheese jar. Full and safe, Milo became the kitchen\'s tiny hero!'),
    LocalChatMessage(
        sender: MessageSender.user,
        imagePath: _kUserImageExample,
        text: 'what do you think about my picture'),
    LocalChatMessage(
        sender: MessageSender.darvis,
        text:
            'This is a mocked response simulating a multi-line reply to test the layout and wrapping behavior of the chat bubbles. This text should wrap to multiple lines naturally.'),
  ];

  @override
  void initState() {
    super.initState();
    _chatService = GetIt.instance<ChatService>();
    _loadMessages();

    // Listen to message stream
    _chatService.messagesStream.listen((messages) {
      if (mounted) {
        setState(() {
          _realMessages = messages;
        });
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _loadMessages() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _chatService.loadMessages(_currentConversationId);
    } catch (e) {
      print('Failed to load messages: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _sendMessage(String text, File? image) async {
    if (text.trim().isEmpty && image == null) return;

    try {
      await _chatService.sendMessage(
        text: text.trim(),
        conversationId: _currentConversationId,
        image: image,
      );
    } catch (e) {
      print('Failed to send message: $e');
      // Show error to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send message: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _onDrawerChanged(bool isOpened) {
    setState(() {
      _isDrawerOpen = isOpened;
    });

    if (isOpened) {
      // Drawer is opening - save current focus state and always unfocus
      _wasTextFieldFocusedBeforeDrawer = _bottomInputKey.currentState?.focusNode.hasFocus ?? false;
      // Always unfocus to hide keyboard when drawer opens
      _bottomInputKey.currentState?.focusNode.unfocus();
    } else {
      // Drawer is closing - only restore focus if it was focused before
      if (_wasTextFieldFocusedBeforeDrawer) {
        // Use a delay to ensure drawer animation completes
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted && _wasTextFieldFocusedBeforeDrawer && !_isDrawerOpen) {
            _bottomInputKey.currentState?.focusNode.requestFocus();
          }
        });
      }
      // Reset the flag after handling
      _wasTextFieldFocusedBeforeDrawer = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      resizeToAvoidBottomInset: true, // Ensures scaffold insets for keyboard
      backgroundColor: DesignTokens.backgroundApp,
      // The side menu that slides in from the left.
      drawer: const _SideMenu(),
      onDrawerChanged: _onDrawerChanged,
      body: Column(
        children: [
          _ChatHeader(onMenuTap: () => _scaffoldKey.currentState?.openDrawer()),

          // The chat messages area, which scrolls independently.
          Expanded(
            child: ListView.builder(
              reverse: true,
              padding: const EdgeInsets.symmetric(
                horizontal: DesignTokens.spacingMd,
              ),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                // To display messages from bottom up, we reverse the list access.
                final message = _messages.reversed.toList()[index];
                return message.sender == MessageSender.darvis
                    ? _DarvisMessageBubble(message: message)
                    : _UserMessageBubble(message: message);
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: _BottomInputBar(
          key: _bottomInputKey,
          onSend: _sendMessage,
        ),
      ),
    );
  }
}

// --- Header Widget ---

class _ChatHeader extends StatelessWidget {
  final VoidCallback onMenuTap;
  const _ChatHeader({required this.onMenuTap});

  @override
  Widget build(BuildContext context) {
    // Using SafeArea to avoid system UI (status bar).
    return SafeArea(
      bottom: false,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingMd,
          vertical: DesignTokens.spacingSm,
        ),
        child: Column(
          children: [
            const SizedBox(height: 20), // Increase this value for more space
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  icon: SvgPicture.asset(_kChatMenuIcon, height: 22),
                  onPressed: onMenuTap,
                ),
                // Wrapping the header title with a GestureDetector to enable tap navigation.
                GestureDetector(
                  onTap: () {
                    GetIt.instance<NavigationService>().navigateToTab(NavigationService.homeTab);
                  },
                  child: const _GradientTitle(text: 'Drix'),
                ),
                IconButton(
                  icon: SvgPicture.asset(_kNewChatIcon, height: 22),
                  onPressed: () {},
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// --- Side Menu (Drawer) Widget ---

class _SideMenu extends StatelessWidget {
  const _SideMenu();

  @override
  Widget build(BuildContext context) {
    // A Drawer is a standard Flutter widget for side menus.
    return Drawer(
      width: MediaQuery.of(context).size.width * 0.55, // Reduced width
      backgroundColor: DesignTokens.backgroundSideMenu,
      child: SafeArea(
        child: Padding(
          padding:
              const EdgeInsets.symmetric(horizontal: DesignTokens.spacingLg),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: DesignTokens.spacingLg),
              Center(child: Image.asset(_kDarvisLogo, height: 80)),
              const SizedBox(height: DesignTokens.spacingXl),

              Center(child: Text('Modes', style: DesignTokens.menuSectionTitleStyle)), // Centered
              const SizedBox(height: DesignTokens.spacingMd),
              _buildMenuItem('Personal Mode'),
              _buildMenuItem('Learn Mode'),
              _buildMenuItem('Lessons Mode'),
              _buildMenuItem('Naija Mode'),

              const SizedBox(height: DesignTokens.spacingXl),
              Center(child: Text('History', style: DesignTokens.menuSectionTitleStyle)), // Centered
              const SizedBox(height: DesignTokens.spacingMd),

              // Scrollable list for history items.
              Expanded(
                child: ListView.builder(
                  itemCount: 15,
                  itemBuilder: (context, index) =>
                      _buildMenuItem('Last time I saw you...'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: DesignTokens.spacingSm),
      child: Text(text, style: DesignTokens.menuItemTextStyle),
    );
  }
}

// --- Chat Bubble Widgets ---

class _DarvisMessageBubble extends StatelessWidget {
  final LocalChatMessage message;
  const _DarvisMessageBubble({required this.message});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: DesignTokens.spacingSm),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: DesignTokens.spacingMd), // Align with first line
            child: SvgPicture.asset(_kDarvisChatIcon, height: 24),
          ),
          const SizedBox(width: 0.5), // Reduced from spacingSm
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(DesignTokens.spacingMd),
                  decoration: const BoxDecoration(
                    color: Colors.transparent,
                  ),
                  child: Text(message.text, style: DesignTokens.chatMessageStyle),
                ),
                Transform.translate(
                  offset: const Offset(0, -12), // Move copy icon up
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(), // Removes minimum size
                    icon: SvgPicture.asset(_kCopyIcon, height: 15, width: 15), // Made slightly larger
                    onPressed: () {
                      Clipboard.setData(ClipboardData(text: message.text));
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Message copied to clipboard'),
                          duration: Duration(seconds: 1),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _UserMessageBubble extends StatelessWidget {
  final LocalChatMessage message;
  const _UserMessageBubble({required this.message});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: DesignTokens.spacingSm),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  padding: const EdgeInsets.all(DesignTokens.spacingMd),
                  decoration: BoxDecoration(
                    color: DesignTokens.backgroundChatBubbleUser,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(DesignTokens.borderRadiusChatBubble),
                      topRight: Radius.circular(DesignTokens.borderRadiusChatBubble),
                      bottomLeft: Radius.circular(DesignTokens.borderRadiusChatBubble),
                      bottomRight: Radius.zero,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (message.imagePath != null)
                        ClipRRect(
                          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
                          child: Image.asset(message.imagePath!),
                        ),
                      if (message.imagePath != null && message.text.isNotEmpty)
                        const SizedBox(height: DesignTokens.spacingSm),
                      if (message.text.isNotEmpty)
                        Text(message.text, style: DesignTokens.chatMessageStyle),
                    ],
                  ),
                ),
                Transform.translate(
                  offset: const Offset(0, -12), // Move copy icon up
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(), // Removes minimum size
                    icon: SvgPicture.asset(_kCopyIcon, height: 15, width: 15), // Made slightly larger
                    onPressed: () {
                      Clipboard.setData(ClipboardData(text: message.text));
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Message copied to clipboard'),
                          duration: Duration(seconds: 1),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// --- Bottom Input Bar Widgets ---

class _BottomInputBar extends StatefulWidget {
  final Function(String text, File? image) onSend;

  const _BottomInputBar({super.key, required this.onSend});

  @override
  State<_BottomInputBar> createState() => _BottomInputBarState();
}

class _BottomInputBarState extends State<_BottomInputBar> {
  final _textController = TextEditingController();
  final _focusNode = FocusNode();
  final _imagePicker = ImagePicker();

  bool _isWebToggleActive = false;
  File? _selectedImage;
  static const double _maxInputHeight = 150.0;

  // Expose focus node for parent access
  FocusNode get focusNode => _focusNode;

  @override
  void initState() {
    super.initState();
    _textController.addListener(() => setState(() {}));
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
        });
      }
    } catch (e) {
      print('Error picking image: $e');
    }
  }



  void _sendMessage() {
    final text = _textController.text.trim();
    if (text.isNotEmpty || _selectedImage != null) {
      widget.onSend(text, _selectedImage);
      _textController.clear();
      setState(() {
        _selectedImage = null;
      });
      _focusNode.unfocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool hasText = _textController.text.isNotEmpty;
    final bool hasImage = _selectedImage != null;

    return SafeArea(
      child: Container(
        margin: const EdgeInsets.all(DesignTokens.spacingSm),
        // Remove outer borderRadius; split into two stacked containers
        decoration: const BoxDecoration(
          color: Colors.transparent,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Image Preview (only visible when image is attached)
            if (_selectedImage != null)
              Align(
                alignment: Alignment.centerLeft,
                child: Container(
                  margin: const EdgeInsets.only(
                    left: DesignTokens.spacingSm,
                    right: DesignTokens.spacingSm,
                    top: DesignTokens.spacingSm,
                  ),
                  child: Stack(
                    children: [
                      Container(
                        height: 80,
                        width: 80,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
                          image: DecorationImage(
                            image: FileImage(_selectedImage!),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      Positioned(
                        top: -5,
                        right: -5,
                        child: GestureDetector(
                          onTap: () => setState(() => _selectedImage = null),
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: Colors.black54,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // TEXT FIELD SECTION (top rounded only)
            Container(
              decoration: const BoxDecoration(
                color: DesignTokens.modeToggleInactiveBackground,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(DesignTokens.borderRadiusXl),
                  topRight: Radius.circular(DesignTokens.borderRadiusXl),
                ),
              ),
              constraints: const BoxConstraints(
                maxHeight: _maxInputHeight,
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: DesignTokens.spacingMd,
              ),
              child: TextField(
                controller: _textController,
                focusNode: _focusNode,
                maxLines: null,
                minLines: 1,
                style: DesignTokens.bodyStyle.copyWith(
                  color: DesignTokens.textPrimary,
                ),
                decoration: InputDecoration(
                  isDense: true,
                  hintText: _selectedImage != null
                      ? 'Add a caption...'
                      : 'Message Darvis',
                  hintStyle: DesignTokens.textInputPlaceholderStyle.copyWith(
                    color: DesignTokens.modeToggleActiveBackground,
                    fontSize: DesignTokens.spacingMd, // using a token-based size
                  ),
                  border: InputBorder.none,
                  filled: false, // Already have parent background
                  contentPadding: const EdgeInsets.symmetric(
                    vertical: DesignTokens.spacingMd,
                  ),
                ),
              ),
            ),

            // ACTION BAR (all corners square)
            Container(
              decoration: const BoxDecoration(
                color: DesignTokens.modeToggleInactiveBackground,
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: DesignTokens.spacingMd,
                vertical: DesignTokens.spacingSm,
              ),
              child: Row(
                children: [
                  // Plus icon (larger)
                  IconButton(
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    icon: SizedBox(
                      width: 20, // INCREASE SIZE HERE
                      height: 20,
                      child: SvgPicture.asset(_kPlusIcon),
                    ),
                    onPressed: _pickImage,
                  ),

                  const SizedBox(width: DesignTokens.spacingMd),

                  // Web toggle (hidden when image is attached)
                  if (!hasImage)
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 200),
                      child: _WebToggleButton(
                        isActive: _isWebToggleActive,
                        onTap: () => setState(() => _isWebToggleActive = !_isWebToggleActive),
                      ),
                    ),

                  if (!hasImage) const SizedBox(width: DesignTokens.spacingMd),

                  // Mic icon (hidden when image is attached)
                  if (!hasImage)
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 200),
                      child: IconButton(
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        icon: SizedBox(
                          width: 20, // INCREASE SIZE
                          height: 20,
                          child: SvgPicture.asset(_kVoiceModeIcon),
                        ),
                        onPressed: () {},
                      ),
                    ),

                  const Spacer(),

                  // Send button (always visible)
                  IconButton(
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    icon: SizedBox(
                      width: 20, // INCREASE SIZE
                      height: 20,
                      child: SvgPicture.asset(
                        _kSendIcon,
                        colorFilter: ColorFilter.mode(
                          DesignTokens.backgroundTextInput, // fixed color for both active and inactive
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    onPressed: (hasText || hasImage) ? _sendMessage : null,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _WebToggleButton extends StatelessWidget {
  final bool isActive;
  final VoidCallback onTap;

  const _WebToggleButton({
    required this.isActive,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingSm,
          vertical: DesignTokens.spacingXs,
        ),
        decoration: BoxDecoration(
          color: isActive
              ? DesignTokens.modeToggleActiveBackground
              : Colors.transparent,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox( // ENSURE CONSISTENT SIZE
              width: 20,
              height: 20,
              child: SvgPicture.asset(
                isActive ? _kSearchActiveIcon : _kSearchInactiveIcon,
                fit: BoxFit.contain,
              ),
            ),
            if (isActive) ...[
              const SizedBox(width: DesignTokens.spacingXs),
              Text(
                'Web',
                style: DesignTokens.bodyStyle.copyWith(
                  color: DesignTokens.textOnLightBackground,
                  fontSize: 12,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}



// --- Reused Helper Widgets ---

class _GradientTitle extends StatelessWidget {
  final String text;
  const _GradientTitle({required this.text});

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      blendMode: BlendMode.srcIn,
      shaderCallback: (bounds) =>
          DesignTokens.appNameHeaderGradient.createShader(
        Rect.fromLTWH(0, 0, bounds.width, bounds.height),
      ),
      child:
          Text(text, style: DesignTokens.appNameStyle.copyWith(fontSize: 40)),
    );
  }
}
