import 'dart:async';
import 'dart:collection';
import 'dart:convert';

import 'package:isar/isar.dart';

import 'api_service.dart';
import '../models/isar_models.dart';

/// Offline-first sync engine for managing data synchronization
class SyncEngine {
  final ApiService _apiService;
  final Isar _isar;

  final Queue<LocalSyncOperation> _syncQueue = Queue<LocalSyncOperation>();
  final StreamController<SyncStatus> _syncStatusController = StreamController<SyncStatus>.broadcast();

  Timer? _syncTimer;
  bool _isSyncing = false;

  SyncEngine({
    required ApiService apiService,
    required Isar isar,
  }) : _apiService = apiService, _isar = isar;

  /// Stream of sync status updates
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;

  /// Current sync status
  SyncStatus get currentStatus => _syncStatusController.hasListener
      ? SyncStatus.synced
      : SyncStatus.offline;

  /// Initialize the sync engine
  Future<void> initialize() async {
    // Load pending sync operations from database
    await _loadPendingSyncOperations();

    // Start periodic sync
    _startPeriodicSync();
  }

  /// Load pending sync operations from database
  Future<void> _loadPendingSyncOperations() async {
    // TODO: Load pending operations from Isar and add to queue
    // This will be implemented once Isar schemas are generated
  }

  /// Add operation to sync queue
  void queueOperation(SyncOperation operation) {
    // Convert SyncOperation to LocalSyncOperation for queue
    final localOp = LocalSyncOperation()
      ..operationType = operation.type.name
      ..entityType = operation.entityType
      ..entityId = operation.entityId
      ..localId = operation.localId
      ..dataJson = jsonEncode(operation.data)
      ..timestamp = operation.timestamp
      ..retryCount = operation.retryCount
      ..status = 'pending';

    _syncQueue.add(localOp);
    _triggerSync();
  }

  /// Queue a create operation
  void queueCreate<T>({
    required String entityType,
    required Map<String, dynamic> data,
    required String localId,
  }) {
    queueOperation(SyncOperation(
      type: SyncOperationType.create,
      entityType: entityType,
      data: data,
      localId: localId,
      timestamp: DateTime.now(),
    ));
  }

  /// Queue an update operation
  void queueUpdate<T>({
    required String entityType,
    required String id,
    required Map<String, dynamic> data,
  }) {
    queueOperation(SyncOperation(
      type: SyncOperationType.update,
      entityType: entityType,
      entityId: id,
      data: data,
      timestamp: DateTime.now(),
    ));
  }

  /// Queue a delete operation
  void queueDelete<T>({
    required String entityType,
    required String id,
  }) {
    queueOperation(SyncOperation(
      type: SyncOperationType.delete,
      entityType: entityType,
      entityId: id,
      data: {}, // Empty data for delete operations
      timestamp: DateTime.now(),
    ));
  }

  /// Trigger immediate sync
  Future<void> sync() async {
    if (_isSyncing) return;
    
    _isSyncing = true;
    _syncStatusController.add(SyncStatus.syncing);

    try {
      while (_syncQueue.isNotEmpty) {
        final operation = _syncQueue.removeFirst();
        await _processLocalOperation(operation);
      }
      
      _syncStatusController.add(SyncStatus.synced);
    } catch (e) {
      _syncStatusController.add(SyncStatus.error);
      print('Sync error: $e');
    } finally {
      _isSyncing = false;
    }
  }

  /// Process a single local sync operation
  Future<void> _processLocalOperation(LocalSyncOperation operation) async {
    try {
      final data = jsonDecode(operation.dataJson) as Map<String, dynamic>;

      switch (operation.operationType) {
        case 'create':
          await _processCreateLocalOperation(operation, data);
          break;
        case 'update':
          await _processUpdateLocalOperation(operation, data);
          break;
        case 'delete':
          await _processDeleteLocalOperation(operation);
          break;
      }
    } catch (e) {
      // Re-queue operation for retry
      operation.retryCount++;
      operation.status = 'failed';
      _syncQueue.add(operation);

      // If max retries reached, mark as failed
      if (operation.retryCount >= 3) {
        print('Operation failed after max retries: ${operation.entityType}');
        // TODO: Store failed operations for manual resolution
      }

      rethrow;
    }
  }

  /// Process create operation
  Future<void> _processCreateLocalOperation(LocalSyncOperation operation, Map<String, dynamic> data) async {
    switch (operation.entityType) {
      case 'task':
        final response = await _apiService.createTask(
          title: data['title'],
          description: data['description'],
        );
        // TODO: Update local entity with server ID
        break;
      case 'note':
        final response = await _apiService.createNote(
          title: data['title'],
          content: data['content'],
        );
        // TODO: Update local entity with server ID
        break;
      default:
        throw Exception('Unknown entity type: ${operation.entityType}');
    }
  }

  /// Process update operation
  Future<void> _processUpdateLocalOperation(LocalSyncOperation operation, Map<String, dynamic> data) async {
    switch (operation.entityType) {
      case 'task':
        await _apiService.updateTask(
          taskId: operation.entityId!,
          updates: data,
        );
        break;
      default:
        throw Exception('Unknown entity type: ${operation.entityType}');
    }
  }

  /// Process delete operation
  Future<void> _processDeleteLocalOperation(LocalSyncOperation operation) async {
    // TODO: Implement delete operations when API supports them
    print('Delete operation not yet implemented for ${operation.entityType}');
  }

  /// Start periodic sync timer
  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _triggerSync();
    });
  }

  /// Queue a create operation for sync
  Future<void> queueCreateOperation({
    required String entityType,
    required String entityId,
    required Map<String, dynamic> data,
  }) async {
    final operation = LocalSyncOperation()
      ..operationType = 'create'
      ..entityType = entityType
      ..entityId = entityId
      ..dataJson = jsonEncode(data)
      ..timestamp = DateTime.now()
      ..retryCount = 0
      ..status = 'pending';

    await _isar.writeTxn(() async {
      await _isar.localSyncOperations.put(operation);
    });

    _syncQueue.add(operation);
    _triggerSync();
  }

  /// Queue an update operation for sync
  Future<void> queueUpdateOperation({
    required String entityType,
    required String entityId,
    required Map<String, dynamic> data,
  }) async {
    final operation = LocalSyncOperation()
      ..operationType = 'update'
      ..entityType = entityType
      ..entityId = entityId
      ..dataJson = jsonEncode(data)
      ..timestamp = DateTime.now()
      ..retryCount = 0
      ..status = 'pending';

    await _isar.writeTxn(() async {
      await _isar.localSyncOperations.put(operation);
    });

    _syncQueue.add(operation);
    _triggerSync();
  }

  /// Queue a delete operation for sync
  Future<void> queueDeleteOperation({
    required String entityType,
    required String entityId,
  }) async {
    final operation = LocalSyncOperation()
      ..operationType = 'delete'
      ..entityType = entityType
      ..entityId = entityId
      ..dataJson = ''
      ..timestamp = DateTime.now()
      ..retryCount = 0
      ..status = 'pending';

    await _isar.writeTxn(() async {
      await _isar.localSyncOperations.put(operation);
    });

    _syncQueue.add(operation);
    _triggerSync();
  }

  /// Trigger sync if not already syncing
  void _triggerSync() {
    if (!_isSyncing && _syncQueue.isNotEmpty) {
      sync();
    }
  }

  /// Dispose of resources
  void dispose() {
    _syncTimer?.cancel();
    _syncStatusController.close();
    // Note: Isar is managed by service locator, don't close it here
  }
}

/// Sync operation model
class SyncOperation {
  final SyncOperationType type;
  final String entityType;
  final String? entityId;
  final String? localId;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final int retryCount;

  SyncOperation({
    required this.type,
    required this.entityType,
    this.entityId,
    this.localId,
    required this.data,
    required this.timestamp,
    this.retryCount = 0,
  });

  SyncOperation copyWith({
    SyncOperationType? type,
    String? entityType,
    String? entityId,
    String? localId,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    int? retryCount,
  }) {
    return SyncOperation(
      type: type ?? this.type,
      entityType: entityType ?? this.entityType,
      entityId: entityId ?? this.entityId,
      localId: localId ?? this.localId,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      retryCount: retryCount ?? this.retryCount,
    );
  }
}

/// Types of sync operations
enum SyncOperationType { create, update, delete }

/// Sync status states
enum SyncStatus { synced, syncing, offline, error }
