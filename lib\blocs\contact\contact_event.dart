import 'package:equatable/equatable.dart';
import '../../models/contact_models.dart';

/// Events for contact management
abstract class ContactEvent extends Equatable {
  const ContactEvent();

  @override
  List<Object?> get props => [];
}

/// Load all contacts
class LoadContacts extends ContactEvent {
  const LoadContacts();
}

/// Search contacts
class SearchContacts extends ContactEvent {
  final String query;

  const SearchContacts(this.query);

  @override
  List<Object?> get props => [query];
}

/// Create new contact
class CreateContact extends ContactEvent {
  final ContactModel contact;

  const CreateContact(this.contact);

  @override
  List<Object?> get props => [contact];
}

/// Update existing contact
class UpdateContact extends ContactEvent {
  final ContactModel contact;

  const UpdateContact(this.contact);

  @override
  List<Object?> get props => [contact];
}

/// Delete contact
class DeleteContact extends ContactEvent {
  final String contactId;

  const DeleteContact(this.contactId);

  @override
  List<Object?> get props => [contactId];
}

/// Bulk delete contacts
class BulkDeleteContacts extends ContactEvent {
  final List<String> contactIds;

  const BulkDeleteContacts(this.contactIds);

  @override
  List<Object?> get props => [contactIds];
}

/// Export contact as VCF
class ExportContactAsVcf extends ContactEvent {
  final ContactModel contact;

  const ExportContactAsVcf(this.contact);

  @override
  List<Object?> get props => [contact];
}

/// Export multiple contacts as VCF
class ExportContactsAsVcf extends ContactEvent {
  final List<ContactModel> contacts;

  const ExportContactsAsVcf(this.contacts);

  @override
  List<Object?> get props => [contacts];
}

/// Import contacts from device
class ImportContactsFromDevice extends ContactEvent {
  const ImportContactsFromDevice();
}

/// Sync contacts with backend
class SyncContacts extends ContactEvent {
  const SyncContacts();
}

/// Add social media profile to contact
class AddSocialMediaProfile extends ContactEvent {
  final String contactId;
  final String platform;
  final String username;

  const AddSocialMediaProfile({
    required this.contactId,
    required this.platform,
    required this.username,
  });

  @override
  List<Object?> get props => [contactId, platform, username];
}

/// Remove social media profile from contact
class RemoveSocialMediaProfile extends ContactEvent {
  final String contactId;
  final String platform;

  const RemoveSocialMediaProfile({
    required this.contactId,
    required this.platform,
  });

  @override
  List<Object?> get props => [contactId, platform];
}

/// Open social media profile
class OpenSocialMediaProfile extends ContactEvent {
  final String platform;
  final String username;

  const OpenSocialMediaProfile({
    required this.platform,
    required this.username,
  });

  @override
  List<Object?> get props => [platform, username];
}

/// Filter contacts by tag
class FilterContactsByTag extends ContactEvent {
  final String? tag;

  const FilterContactsByTag(this.tag);

  @override
  List<Object?> get props => [tag];
}

/// Sort contacts
class SortContacts extends ContactEvent {
  final ContactSortOption sortOption;

  const SortContacts(this.sortOption);

  @override
  List<Object?> get props => [sortOption];
}

/// Contact sort options
enum ContactSortOption {
  nameAsc,
  nameDesc,
  dateCreatedAsc,
  dateCreatedDesc,
  lastContactedAsc,
  lastContactedDesc,
}
