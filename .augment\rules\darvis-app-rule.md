---
type: "agent_requested"
description: "Example description"
---

# Rules.md - AI Coding Agent Guidelines

## Requirements for implementing a fix:

**Non-destructive approach**: Ensure all fixes preserve existing functionality and don't break current working features

**Systems thinking**: Consider the broader impact of code changes on the entire application architecture, including state management, component interactions, and user experience flows

**Root Cause Identification**: Focus on identifying and addressing the underlying cause of a problem, not just its surface symptoms. Avoid patchwork solutions that only suppress visible issues without resolving their origin. Use debugging tools, logs, and historical context to trace the issue to its source before applying any fix.

**Consequence analysis**: Evaluate potential side effects, performance implications, and edge cases before implementing changes

**High-level architectural perspective**: Think holistically about how these fixes integrate with the overall system design, data flow, and component hierarchy

## Requirements for implementing designs:

**Design Fidelity**: Implement designs with pixel-perfect accuracy while maintaining responsive behavior across different screen sizes and orientations

**Design Token Adherence**: All styling must reference the centralized design token system (lib/utils/design_tokens.dart) - no hard-coded values permitted

**Component Reusability**: Create modular, reusable components that can be composed into larger UI elements while maintaining design consistency

**Accessibility First**: Ensure all design implementations include proper semantic labels, color contrast ratios, and support for assistive technologies

**Cross-Platform Consistency**: Verify designs work consistently across iOS and Android while respecting platform-specific conventions where appropriate

**Performance-Conscious Design**: Optimize asset usage, minimize widget rebuilds, and consider animation performance impact during implementation

## Requirements for implementing features:

**User Story Validation**: Ensure the implementation fully satisfies the intended user journey and acceptance criteria before marking complete

**State Management Integration**: Properly integrate with the established BLoC pattern, ensuring new features follow existing state management conventions and don't introduce architectural inconsistencies

**Offline-First Compliance**: All new features must work seamlessly in offline mode, with data persisted to Isar and proper sync handling through the SyncEngine

**API Integration Standards**: Follow established patterns for backend communication using the centralized ApiService, including proper error handling and loading states

**Testing Coverage**: Include appropriate unit tests for business logic, widget tests for UI components, and integration tests for complex user flows

**Performance Validation**: Verify that new features don't negatively impact app startup time, memory usage, or overall responsiveness

**Security Considerations**: Ensure new features properly handle sensitive data, respect user privacy, and maintain security best practices

## Code Quality Standards:

**Documentation Requirements**: All public methods, complex logic, and architectural decisions must be documented with clear, concise comments

**Error Handling**: Implement comprehensive error handling with user-friendly messages and graceful degradation paths

**Code Consistency**: Follow established naming conventions, file organization patterns, and coding style throughout the project

**Dependency Management**: Minimize external dependencies and ensure any new packages align with the project's architectural goals

## Post-Implementation Summary Requirement:

At the conclusion of every fix, design implementation, or feature addition, include a concise summary describing:

- What was changed and why
- How the change addresses the issue/requirement
- In what ways the implementation adheres to the requirements above
- Any potential areas for future optimization or consideration
- Impact on related systems or components

## Quality Gates:

Before considering any implementation complete:

- [ ] All automated tests pass
- [ ] Manual testing completed on Android
- [ ] Code review completed (if applicable)
- [ ] Performance impact assessed
- [ ] Documentation updated
- [ ] Post-implementation summary provided