import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:path_provider/path_provider.dart';

import 'api_service.dart';
import 'auth_service_interface.dart';
import 'firebase_auth_service.dart';
import 'auth_interceptor.dart';
import 'livekit_service.dart';
import 'sync_engine.dart';
import 'navigation_service.dart';
import 'greeting_service.dart';
import 'notification_service.dart';
import 'cloudinary_service.dart';
import 'profile_service.dart';
import 'share_intent_service.dart';
import 'social_media_service.dart';
import 'contact_service.dart';
import 'chat_service.dart';
import 'vcf_service.dart';
import 'device_contacts_service.dart';
import '../blocs/auth/auth_bloc.dart';
import '../blocs/dashboard/dashboard_bloc.dart';
import '../blocs/notification/notification_bloc.dart';
import '../blocs/profile/profile_bloc.dart';
import '../blocs/notes/notes_bloc.dart';
import '../blocs/tasks/tasks_bloc.dart';
import '../blocs/calendar/calendar_bloc.dart';
import '../blocs/contact/contact_bloc.dart';
import '../models/isar_models.dart';
import '../models/contact_models.dart';
import '../models/chat_models.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:image_picker/image_picker.dart';
import 'package:isar/isar.dart';

final GetIt getIt = GetIt.instance;

Future<void> setupServiceLocator() async {
  // Initialize Isar database first (required by multiple services)
  final dir = await getApplicationDocumentsDirectory();
  final isar = await Isar.open(
    [
      LocalTaskSchema,
      LocalNoteSchema,
      LocalUserSchema,
      LocalSyncOperationSchema,
      LocalDashboardCacheSchema,
      ContactModelSchema,
      ChatMessageSchema,
      ChatConversationSchema,
    ],
    directory: dir.path,
  );

  // Register Isar as singleton
  getIt.registerSingleton<Isar>(isar);

  // Core services
  getIt.registerLazySingleton<Dio>(() => _createDio());
  getIt.registerLazySingleton<FlutterSecureStorage>(
    () => const FlutterSecureStorage(
      aOptions: AndroidOptions(
        encryptedSharedPreferences: true,
      ),
      iOptions: IOSOptions(
        accessibility: KeychainAccessibility.first_unlock_this_device,
      ),
    ),
  );

  // Business services
  getIt.registerLazySingleton<ApiService>(
    () => ApiService(getIt<Dio>()),
  );

  getIt.registerLazySingleton<GreetingService>(
    () => GreetingService(
      apiService: getIt<ApiService>(),
      secureStorage: getIt<FlutterSecureStorage>(),
    ),
  );

  // Phase 4 Smart Capture services
  getIt.registerLazySingleton<ShareIntentService>(
    () => ShareIntentService(),
  );

  // Social Media Service
  getIt.registerLazySingleton<SocialMediaService>(
    () => SocialMediaService(),
  );

  // Contact Service
  getIt.registerLazySingleton<ContactService>(
    () => ContactService(
      isar: getIt<Isar>(),
      apiService: getIt<ApiService>(),
      syncEngine: getIt<SyncEngine>(),
      vcfService: VcfService(),
      deviceContactsService: DeviceContactsService(),
      socialMediaService: getIt<SocialMediaService>(),
    ),
  );

  // Chat Service
  getIt.registerLazySingleton<ChatService>(
    () => ChatService(
      isar: getIt<Isar>(),
      apiService: getIt<ApiService>(),
      cloudinaryService: getIt<CloudinaryService>(),
    ),
  );

  // External dependencies
  getIt.registerLazySingleton<FlutterLocalNotificationsPlugin>(
    () => FlutterLocalNotificationsPlugin(),
  );

  getIt.registerLazySingleton<ImagePicker>(
    () => ImagePicker(),
  );

  // Cloudinary service with actual credentials from backend
  getIt.registerLazySingleton<CloudinaryService>(
    () => CloudinaryService(
      cloudName: 'drixai',
      apiKey: '245163735861457',
      apiSecret: '0X-QlKsoYErrlwA-GxmO6LpBZqg',
      uploadPreset: 'drix_profiles', // Default preset for profile pictures
    ),
  );

  getIt.registerLazySingleton<NotificationService>(
    () => NotificationService(
      localNotifications: getIt<FlutterLocalNotificationsPlugin>(),
      isar: getIt<Isar>(),
      apiService: getIt<ApiService>(),
    ),
  );

  getIt.registerLazySingleton<ProfileService>(
    () => ProfileService(
      isar: getIt<Isar>(),
      cloudinaryService: getIt<CloudinaryService>(),
      apiService: getIt<ApiService>(),
      imagePicker: getIt<ImagePicker>(),
    ),
  );
  
  // Register Firebase Auth Service for mobile
  getIt.registerLazySingleton<AuthServiceInterface>(
    () => FirebaseAuthService(
      apiService: getIt<ApiService>(),
      secureStorage: getIt<FlutterSecureStorage>(),
    ),
  );
  
  getIt.registerLazySingleton<LiveKitService>(
    () => LiveKitService(),
  );

  getIt.registerLazySingleton<SyncEngine>(
    () => SyncEngine(
      apiService: getIt<ApiService>(),
      isar: getIt<Isar>(),
    ),
  );

  // Navigation service
  getIt.registerLazySingleton<NavigationService>(
    () => NavigationService(),
  );

  // BLoCs
  getIt.registerFactory<AuthBloc>(
    () => AuthBloc(
      authService: getIt<AuthServiceInterface>(),
    ),
  );

  getIt.registerFactory<DashboardBloc>(
    () => DashboardBloc(
      apiService: getIt<ApiService>(),
      greetingService: getIt<GreetingService>(),
    ),
  );

  getIt.registerFactory<NotificationBloc>(
    () => NotificationBloc(
      notificationService: getIt<NotificationService>(),
    ),
  );

  getIt.registerFactory<ProfileBloc>(
    () => ProfileBloc(
      profileService: getIt<ProfileService>(),
    ),
  );

  getIt.registerFactory<NotesBloc>(
    () => NotesBloc(
      isar: getIt<Isar>(),
      apiService: getIt<ApiService>(),
      syncEngine: getIt<SyncEngine>(),
    ),
  );

  getIt.registerFactory<TasksBloc>(
    () => TasksBloc(
      isar: getIt<Isar>(),
      apiService: getIt<ApiService>(),
      syncEngine: getIt<SyncEngine>(),
    ),
  );

  getIt.registerFactory<CalendarBloc>(
    () => CalendarBloc(
      apiService: getIt<ApiService>(),
    ),
  );

  getIt.registerFactory<ContactBloc>(
    () => ContactBloc(
      contactService: getIt<ContactService>(),
    ),
  );

  // Add auth interceptor after all services are registered to avoid circular dependency
  final dio = getIt<Dio>();
  final authService = getIt<AuthServiceInterface>();
  dio.interceptors.add(AuthInterceptor(authService));
}

Dio _createDio() {
  final dio = Dio();

  // Base configuration
  dio.options.baseUrl = 'https://api.darvis.app'; // TODO: Replace with actual API URL
  dio.options.connectTimeout = const Duration(seconds: 30);
  dio.options.receiveTimeout = const Duration(seconds: 30);
  dio.options.sendTimeout = const Duration(seconds: 30);

  // Add interceptors
  dio.interceptors.add(LogInterceptor(
    requestBody: true,
    responseBody: true,
    logPrint: (object) {
      // TODO: Use proper logging service
      print(object);
    },
  ));

  // TODO: Add certificate pinning
  // TODO: Add retry interceptor

  return dio;
}
